{"level":"info","message":"📴 Received SIGTERM. Starting graceful shutdown...","timestamp":"2025-06-04 00:00:02:02"}
{"level":"info","message":"Initializing database connections...","timestamp":"2025-06-04 00:00:03:03"}
{"level":"info","message":"Connecting to Neo4j database...","timestamp":"2025-06-04 00:00:03:03"}
{"level":"info","message":"✅ Neo4j connection established successfully","timestamp":"2025-06-04 00:00:03:03"}
{"level":"info","message":"Initializing Neo4j schema...","timestamp":"2025-06-04 00:00:03:03"}
{"level":"info","message":"✅ Neo4j schema initialized successfully","timestamp":"2025-06-04 00:00:03:03"}
{"level":"info","message":"✅ Neo4j connected successfully","timestamp":"2025-06-04 00:00:03:03"}
{"level":"info","message":"Connecting to Weaviate vector database...","timestamp":"2025-06-04 00:00:03:03"}
{"level":"info","message":"✅ Weaviate connection established successfully","timestamp":"2025-06-04 00:00:03:03"}
{"level":"info","message":"Initializing Weaviate schemas...","timestamp":"2025-06-04 00:00:03:03"}
{"error":"usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","level":"error","message":"Failed to create schema: Supplement","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 00:00:03:03"}
{"error":"usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","level":"error","message":"❌ Failed to initialize Weaviate schemas","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 00:00:03:03"}
{"level":"warn","message":"⚠️ Failed to connect to Weaviate, continuing without vector database: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 00:00:03:03"}
{"level":"info","message":"✅ Weaviate connected successfully","timestamp":"2025-06-04 00:00:03:03"}
{"level":"info","message":"Connecting to Redis cache...","timestamp":"2025-06-04 00:00:03:03"}
{"level":"info","message":"Redis client connected","timestamp":"2025-06-04 00:00:03:03"}
{"level":"info","message":"Redis client ready","timestamp":"2025-06-04 00:00:03:03"}
{"level":"info","message":"✅ Redis connection established successfully","timestamp":"2025-06-04 00:00:03:03"}
{"level":"info","message":"✅ Redis connected successfully","timestamp":"2025-06-04 00:00:03:03"}
{"level":"info","message":"Connecting to MongoDB...","timestamp":"2025-06-04 00:00:03:03"}
{"level":"info","message":"✅ MongoDB connection established successfully","timestamp":"2025-06-04 00:00:03:03"}
{"level":"info","message":"✅ MongoDB connected successfully","timestamp":"2025-06-04 00:00:03:03"}
{"level":"info","message":"🚀 All databases connected successfully","timestamp":"2025-06-04 00:00:03:03"}
{"level":"info","message":"🚀 Server running on port 3000","timestamp":"2025-06-04 00:00:03:03"}
{"level":"info","message":"📊 Environment: development","timestamp":"2025-06-04 00:00:03:03"}
{"level":"info","message":"🔗 API URL: http://localhost:3000/api","timestamp":"2025-06-04 00:00:03:03"}
{"level":"info","message":"🔌 AG-UI WebSocket: ws://localhost:3000/agui/ws","timestamp":"2025-06-04 00:00:03:03"}
{"level":"info","message":"💚 Health check: http://localhost:3000/health","timestamp":"2025-06-04 00:00:03:03"}
{"level":"info","message":"AG-UI WebSocket connection established: 02a669f3-9be5-4cb6-8380-b54ccc2b1022","timestamp":"2025-06-04 00:00:08:08"}
{"level":"info","message":"AG-UI WebSocket connection established: 7f506975-c485-4bde-81e8-24f490386965","timestamp":"2025-06-04 00:00:08:08"}
{"level":"info","message":"AG-UI WebSocket connection established: bb950cbf-7b54-40d2-8889-871a5767e56d","timestamp":"2025-06-04 00:00:08:08"}
{"level":"info","message":"AG-UI WebSocket connection established: c347bfba-1100-4984-a472-40a2c5d3ad43","timestamp":"2025-06-04 00:00:08:08"}
{"level":"info","message":"AG-UI WebSocket connection established: de876eda-d1bf-4510-9345-ceb50e24635c","timestamp":"2025-06-04 00:00:08:08"}
{"level":"info","message":"Initializing database connections...","timestamp":"2025-06-04 00:00:08:08"}
{"level":"info","message":"Connecting to Neo4j database...","timestamp":"2025-06-04 00:00:08:08"}
{"level":"info","message":"✅ Neo4j connection established successfully","timestamp":"2025-06-04 00:00:08:08"}
{"level":"info","message":"Initializing Neo4j schema...","timestamp":"2025-06-04 00:00:08:08"}
{"level":"info","message":"✅ Neo4j schema initialized successfully","timestamp":"2025-06-04 00:00:08:08"}
{"level":"info","message":"✅ Neo4j connected successfully","timestamp":"2025-06-04 00:00:08:08"}
{"level":"info","message":"Connecting to Weaviate vector database...","timestamp":"2025-06-04 00:00:08:08"}
{"level":"info","message":"✅ Weaviate connection established successfully","timestamp":"2025-06-04 00:00:08:08"}
{"level":"info","message":"Initializing Weaviate schemas...","timestamp":"2025-06-04 00:00:08:08"}
{"error":"usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","level":"error","message":"Failed to create schema: Supplement","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 00:00:08:08"}
{"error":"usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","level":"error","message":"❌ Failed to initialize Weaviate schemas","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 00:00:08:08"}
{"level":"warn","message":"⚠️ Failed to connect to Weaviate, continuing without vector database: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 00:00:08:08"}
{"level":"info","message":"✅ Weaviate connected successfully","timestamp":"2025-06-04 00:00:08:08"}
{"level":"info","message":"Connecting to Redis cache...","timestamp":"2025-06-04 00:00:08:08"}
{"level":"info","message":"Redis client connected","timestamp":"2025-06-04 00:00:08:08"}
{"level":"info","message":"Redis client ready","timestamp":"2025-06-04 00:00:08:08"}
{"level":"info","message":"✅ Redis connection established successfully","timestamp":"2025-06-04 00:00:08:08"}
{"level":"info","message":"✅ Redis connected successfully","timestamp":"2025-06-04 00:00:08:08"}
{"level":"info","message":"Connecting to MongoDB...","timestamp":"2025-06-04 00:00:08:08"}
{"level":"info","message":"✅ MongoDB connection established successfully","timestamp":"2025-06-04 00:00:08:08"}
{"level":"info","message":"AG-UI WebSocket connection closed: de876eda-d1bf-4510-9345-ceb50e24635c","timestamp":"2025-06-04 00:00:12:012"}
{"level":"info","message":"AG-UI WebSocket connection closed: 7f506975-c485-4bde-81e8-24f490386965","timestamp":"2025-06-04 00:00:12:012"}
{"level":"info","message":"Initializing database connections...","timestamp":"2025-06-04 00:00:23:023"}
{"level":"info","message":"Connecting to Neo4j database...","timestamp":"2025-06-04 00:00:23:023"}
{"level":"info","message":"✅ Neo4j connection established successfully","timestamp":"2025-06-04 00:00:23:023"}
{"level":"info","message":"Initializing Neo4j schema...","timestamp":"2025-06-04 00:00:23:023"}
{"level":"info","message":"✅ Neo4j schema initialized successfully","timestamp":"2025-06-04 00:00:23:023"}
{"level":"info","message":"✅ Neo4j connected successfully","timestamp":"2025-06-04 00:00:23:023"}
{"level":"info","message":"Connecting to Weaviate vector database...","timestamp":"2025-06-04 00:00:23:023"}
{"level":"info","message":"✅ Weaviate connection established successfully","timestamp":"2025-06-04 00:00:23:023"}
{"level":"info","message":"Initializing Weaviate schemas...","timestamp":"2025-06-04 00:00:23:023"}
{"error":"usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","level":"error","message":"Failed to create schema: Supplement","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 00:00:23:023"}
{"error":"usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","level":"error","message":"❌ Failed to initialize Weaviate schemas","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 00:00:23:023"}
{"level":"warn","message":"⚠️ Failed to connect to Weaviate, continuing without vector database: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 00:00:23:023"}
{"level":"info","message":"✅ Weaviate connected successfully","timestamp":"2025-06-04 00:00:23:023"}
{"level":"info","message":"Connecting to Redis cache...","timestamp":"2025-06-04 00:00:23:023"}
{"level":"info","message":"Redis client connected","timestamp":"2025-06-04 00:00:23:023"}
{"level":"info","message":"Redis client ready","timestamp":"2025-06-04 00:00:23:023"}
{"level":"info","message":"✅ Redis connection established successfully","timestamp":"2025-06-04 00:00:23:023"}
{"level":"info","message":"✅ Redis connected successfully","timestamp":"2025-06-04 00:00:23:023"}
{"level":"info","message":"Connecting to MongoDB...","timestamp":"2025-06-04 00:00:23:023"}
{"level":"info","message":"✅ MongoDB connection established successfully","timestamp":"2025-06-04 00:00:23:023"}
{"level":"info","message":"Initializing database connections...","timestamp":"2025-06-04 00:00:48:048"}
{"level":"info","message":"Connecting to Neo4j database...","timestamp":"2025-06-04 00:00:48:048"}
{"level":"info","message":"✅ Neo4j connection established successfully","timestamp":"2025-06-04 00:00:48:048"}
{"level":"info","message":"Initializing Neo4j schema...","timestamp":"2025-06-04 00:00:48:048"}
{"level":"info","message":"✅ Neo4j schema initialized successfully","timestamp":"2025-06-04 00:00:48:048"}
{"level":"info","message":"✅ Neo4j connected successfully","timestamp":"2025-06-04 00:00:48:048"}
{"level":"info","message":"Connecting to Weaviate vector database...","timestamp":"2025-06-04 00:00:48:048"}
{"level":"info","message":"✅ Weaviate connection established successfully","timestamp":"2025-06-04 00:00:48:048"}
{"level":"info","message":"Initializing Weaviate schemas...","timestamp":"2025-06-04 00:00:48:048"}
{"error":"usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","level":"error","message":"Failed to create schema: Supplement","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 00:00:48:048"}
{"error":"usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","level":"error","message":"❌ Failed to initialize Weaviate schemas","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 00:00:48:048"}
{"level":"warn","message":"⚠️ Failed to connect to Weaviate, continuing without vector database: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 00:00:48:048"}
{"level":"info","message":"✅ Weaviate connected successfully","timestamp":"2025-06-04 00:00:48:048"}
{"level":"info","message":"Connecting to Redis cache...","timestamp":"2025-06-04 00:00:48:048"}
{"level":"info","message":"Redis client connected","timestamp":"2025-06-04 00:00:48:048"}
{"level":"info","message":"Redis client ready","timestamp":"2025-06-04 00:00:48:048"}
{"level":"info","message":"✅ Redis connection established successfully","timestamp":"2025-06-04 00:00:48:048"}
{"level":"info","message":"✅ Redis connected successfully","timestamp":"2025-06-04 00:00:48:048"}
{"level":"info","message":"Connecting to MongoDB...","timestamp":"2025-06-04 00:00:48:048"}
{"level":"info","message":"✅ MongoDB connection established successfully","timestamp":"2025-06-04 00:00:48:048"}
{"level":"info","message":"✅ MongoDB connected successfully","timestamp":"2025-06-04 00:00:48:048"}
{"level":"info","message":"🚀 All databases connected successfully","timestamp":"2025-06-04 00:00:48:048"}
{"level":"info","message":"🚀 Server running on port 3001","timestamp":"2025-06-04 00:00:48:048"}
{"level":"info","message":"📊 Environment: development","timestamp":"2025-06-04 00:00:48:048"}
{"level":"info","message":"🔗 API URL: http://localhost:3001/api","timestamp":"2025-06-04 00:00:48:048"}
{"level":"info","message":"🔌 AG-UI WebSocket: ws://localhost:3001/agui/ws","timestamp":"2025-06-04 00:00:48:048"}
{"level":"info","message":"💚 Health check: http://localhost:3001/health","timestamp":"2025-06-04 00:00:48:048"}
{"level":"info","message":"AG-UI WebSocket connection established: 2aa295d9-6d40-43ef-9317-c72e9e265c06","timestamp":"2025-06-04 00:00:49:049"}
{"level":"info","message":"AG-UI WebSocket connection established: 31d93ac7-8214-4ada-879c-e8e8b9adc093","timestamp":"2025-06-04 00:00:51:051"}
{"level":"info","message":"AG-UI WebSocket connection closed: 02a669f3-9be5-4cb6-8380-b54ccc2b1022","timestamp":"2025-06-04 00:01:34:134"}
{"level":"info","message":"AG-UI WebSocket connection closed: c347bfba-1100-4984-a472-40a2c5d3ad43","timestamp":"2025-06-04 00:01:34:134"}
{"level":"info","message":"AG-UI WebSocket connection closed: 2aa295d9-6d40-43ef-9317-c72e9e265c06","timestamp":"2025-06-04 00:01:34:134"}
{"level":"info","message":"AG-UI WebSocket connection closed: bb950cbf-7b54-40d2-8889-871a5767e56d","timestamp":"2025-06-04 00:01:34:134"}
{"level":"info","message":"AG-UI WebSocket connection established: 08f32c04-82e3-49bd-b44a-b40fa5d62b0a","timestamp":"2025-06-04 00:01:34:134"}
{"level":"info","message":"AG-UI WebSocket connection closed: 08f32c04-82e3-49bd-b44a-b40fa5d62b0a","timestamp":"2025-06-04 00:01:34:134"}
{"level":"info","message":"AG-UI WebSocket connection established: bff6b134-3870-4f06-b475-13ebc45e88cd","timestamp":"2025-06-04 00:01:34:134"}
{"level":"info","message":"AG-UI WebSocket connection established: 609d873e-3e30-4b79-b681-5e6be82a00bb","timestamp":"2025-06-04 00:01:35:135"}
{"level":"info","message":"Starting Gemma analysis for supplement: magnaz i żelazo","timestamp":"2025-06-04 00:02:12:212"}
{"level":"error","message":"Error parsing Gemma analysis response: \"undefined\" is not valid JSON","stack":"SyntaxError: \"undefined\" is not valid JSON\n    at JSON.parse (<anonymous>)\n    at GemmaService.parseAnalysisResponse (/home/<USER>/Suplementor/backend/src/services/GemmaService.ts:265:27)\n    at GemmaService.analyzeSupplementData (/home/<USER>/Suplementor/backend/src/services/GemmaService.ts:74:29)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async SupplementResearchAgent.executeResearchPipeline (/home/<USER>/Suplementor/backend/src/agents/SupplementResearchAgent.ts:108:24)","timestamp":"2025-06-04 00:02:12:212"}
{"level":"info","message":"Gemma analysis completed for magnaz i żelazo","timestamp":"2025-06-04 00:02:12:212"}
{"error":"LIMIT: Invalid input. '1.0' is not a valid value. Must be a non-negative integer.","level":"error","message":"Neo4j query failed","parameters":{"limit":1,"searchQuery":"*magnaz i żelazo*"},"query":"\n        CALL db.index.fulltext.queryNodes('supplement_search', $searchQuery) \n        YIELD node, score\n        RETURN node, score\n        UNION\n        CALL db.index.fulltext.queryNodes('ingredient_search', $searchQuery) \n        YIELD node, score\n        RETURN node, score\n        UNION\n        CALL db.index.fulltext.queryNodes('effect_search', $searchQuery) \n        YIELD node, score\n        RETURN node, score\n        UNION\n        CALL db.index.fulltext.queryNodes('study_search', $searchQuery) \n        YIELD node, score\n        RETURN node, score\n        ORDER BY score DESC\n        LIMIT $limit\n      ","stack":"Neo4jError: LIMIT: Invalid input. '1.0' is not a valid value. Must be a non-negative integer.\n\n    at captureStacktrace (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:624:17)\n    at new Result (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:112:23)\n    at Session._run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:224:16)\n    at Session.run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:188:27)\n    at Neo4jConnection.executeQuery (/home/<USER>/Suplementor/backend/src/config/neo4j.ts:64:36)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async GraphService.searchNodes (/home/<USER>/Suplementor/backend/src/services/GraphService.ts:283:18)\n    at async GraphService.updateSupplementKnowledge (/home/<USER>/Suplementor/backend/src/services/GraphService.ts:810:29)\n    at async SupplementResearchAgent.executeResearchPipeline (/home/<USER>/Suplementor/backend/src/agents/SupplementResearchAgent.ts:134:27)","timestamp":"2025-06-04 00:02:13:213"}
{"error":"LIMIT: Invalid input. '1.0' is not a valid value. Must be a non-negative integer.","level":"error","message":"Neo4j query failed","parameters":{"limit":1,"search":"magnaz i żelazo"},"query":"\n          MATCH (n) \n          WHERE n.name CONTAINS $search \n             OR n.description CONTAINS $search\n             OR n.title CONTAINS $search\n         AND (n:Supplement) RETURN n, 1.0 as score ORDER BY n.name LIMIT $limit","stack":"Neo4jError: LIMIT: Invalid input. '1.0' is not a valid value. Must be a non-negative integer.\n\n    at captureStacktrace (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:624:17)\n    at new Result (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:112:23)\n    at Session._run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:224:16)\n    at Session.run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:188:27)\n    at Neo4jConnection.executeQuery (/home/<USER>/Suplementor/backend/src/config/neo4j.ts:64:36)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async GraphService.searchNodes (/home/<USER>/Suplementor/backend/src/services/GraphService.ts:300:18)\n    at async GraphService.updateSupplementKnowledge (/home/<USER>/Suplementor/backend/src/services/GraphService.ts:810:29)\n    at async SupplementResearchAgent.executeResearchPipeline (/home/<USER>/Suplementor/backend/src/agents/SupplementResearchAgent.ts:134:27)","timestamp":"2025-06-04 00:02:13:213"}
{"error":"LIMIT: Invalid input. '1.0' is not a valid value. Must be a non-negative integer.","filters":{"limit":1,"nodeTypes":["Supplement"]},"level":"error","message":"Failed to search nodes","searchQuery":"magnaz i żelazo","stack":"Neo4jError: LIMIT: Invalid input. '1.0' is not a valid value. Must be a non-negative integer.\n\n    at captureStacktrace (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:624:17)\n    at new Result (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:112:23)\n    at Session._run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:224:16)\n    at Session.run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:188:27)\n    at Neo4jConnection.executeQuery (/home/<USER>/Suplementor/backend/src/config/neo4j.ts:64:36)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async GraphService.searchNodes (/home/<USER>/Suplementor/backend/src/services/GraphService.ts:300:18)\n    at async GraphService.updateSupplementKnowledge (/home/<USER>/Suplementor/backend/src/services/GraphService.ts:810:29)\n    at async SupplementResearchAgent.executeResearchPipeline (/home/<USER>/Suplementor/backend/src/agents/SupplementResearchAgent.ts:134:27)","timestamp":"2025-06-04 00:02:13:213"}
{"error":"Failed to search nodes","level":"error","message":"Failed to update supplement knowledge","stack":"Error: Failed to search nodes\n    at GraphService.searchNodes (/home/<USER>/Suplementor/backend/src/services/GraphService.ts:327:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async GraphService.updateSupplementKnowledge (/home/<USER>/Suplementor/backend/src/services/GraphService.ts:810:29)\n    at async SupplementResearchAgent.executeResearchPipeline (/home/<USER>/Suplementor/backend/src/agents/SupplementResearchAgent.ts:134:27)","supplementName":"magnaz i żelazo","timestamp":"2025-06-04 00:02:13:213"}
{"code":"DATABASE_ERROR","isOperational":true,"level":"error","message":"Error in supplement research pipeline: Failed to update supplement knowledge in graph","stack":"Error: Failed to update supplement knowledge in graph\n    at GraphService.updateSupplementKnowledge (/home/<USER>/Suplementor/backend/src/services/GraphService.ts:963:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async SupplementResearchAgent.executeResearchPipeline (/home/<USER>/Suplementor/backend/src/agents/SupplementResearchAgent.ts:134:27)","statusCode":500,"timestamp":"2025-06-04 00:02:13:213"}
{"code":"DATABASE_ERROR","isOperational":true,"level":"error","message":"Agent error for 609d873e-3e30-4b79-b681-5e6be82a00bb-2dabf81a-c7b1-4de3-b72d-c48fa94c258e: Failed to update supplement knowledge in graph","stack":"Error: Failed to update supplement knowledge in graph\n    at GraphService.updateSupplementKnowledge (/home/<USER>/Suplementor/backend/src/services/GraphService.ts:963:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async SupplementResearchAgent.executeResearchPipeline (/home/<USER>/Suplementor/backend/src/agents/SupplementResearchAgent.ts:134:27)","statusCode":500,"timestamp":"2025-06-04 00:02:13:213"}
{"level":"info","message":"📴 Received SIGTERM. Starting graceful shutdown...","timestamp":"2025-06-04 00:03:54:354"}
{"level":"info","message":"📴 Received SIGTERM. Starting graceful shutdown...","timestamp":"2025-06-04 00:03:54:354"}
{"level":"info","message":"Initializing database connections...","timestamp":"2025-06-04 00:03:56:356"}
{"level":"info","message":"Connecting to Neo4j database...","timestamp":"2025-06-04 00:03:56:356"}
{"level":"info","message":"✅ Neo4j connection established successfully","timestamp":"2025-06-04 00:03:56:356"}
{"level":"info","message":"Initializing Neo4j schema...","timestamp":"2025-06-04 00:03:56:356"}
{"level":"info","message":"Initializing database connections...","timestamp":"2025-06-04 00:03:56:356"}
{"level":"info","message":"Connecting to Neo4j database...","timestamp":"2025-06-04 00:03:56:356"}
{"level":"info","message":"✅ Neo4j connection established successfully","timestamp":"2025-06-04 00:03:56:356"}
{"level":"info","message":"Initializing Neo4j schema...","timestamp":"2025-06-04 00:03:56:356"}
{"level":"info","message":"Initializing database connections...","timestamp":"2025-06-04 00:03:56:356"}
{"level":"info","message":"Connecting to Neo4j database...","timestamp":"2025-06-04 00:03:56:356"}
{"level":"info","message":"✅ Neo4j schema initialized successfully","timestamp":"2025-06-04 00:03:56:356"}
{"level":"info","message":"✅ Neo4j connection established successfully","timestamp":"2025-06-04 00:03:56:356"}
{"level":"info","message":"✅ Neo4j connected successfully","timestamp":"2025-06-04 00:03:56:356"}
{"level":"info","message":"Connecting to Weaviate vector database...","timestamp":"2025-06-04 00:03:56:356"}
{"level":"info","message":"Initializing Neo4j schema...","timestamp":"2025-06-04 00:03:56:356"}
{"level":"info","message":"✅ Weaviate connection established successfully","timestamp":"2025-06-04 00:03:56:356"}
{"level":"info","message":"Initializing Weaviate schemas...","timestamp":"2025-06-04 00:03:56:356"}
{"error":"usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","level":"error","message":"Failed to create schema: Supplement","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 00:03:56:356"}
{"error":"usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","level":"error","message":"❌ Failed to initialize Weaviate schemas","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 00:03:56:356"}
{"level":"warn","message":"⚠️ Failed to connect to Weaviate, continuing without vector database: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 00:03:56:356"}
{"level":"info","message":"✅ Weaviate connected successfully","timestamp":"2025-06-04 00:03:56:356"}
{"level":"info","message":"Connecting to Redis cache...","timestamp":"2025-06-04 00:03:56:356"}
{"level":"info","message":"Redis client connected","timestamp":"2025-06-04 00:03:56:356"}
{"level":"info","message":"Redis client ready","timestamp":"2025-06-04 00:03:56:356"}
{"level":"info","message":"✅ Neo4j schema initialized successfully","timestamp":"2025-06-04 00:03:56:356"}
{"level":"info","message":"✅ Neo4j connected successfully","timestamp":"2025-06-04 00:03:56:356"}
{"level":"info","message":"Connecting to Weaviate vector database...","timestamp":"2025-06-04 00:03:56:356"}
{"level":"info","message":"✅ Redis connection established successfully","timestamp":"2025-06-04 00:03:56:356"}
{"level":"info","message":"✅ Redis connected successfully","timestamp":"2025-06-04 00:03:56:356"}
{"level":"info","message":"Connecting to MongoDB...","timestamp":"2025-06-04 00:03:56:356"}
{"level":"info","message":"✅ Weaviate connection established successfully","timestamp":"2025-06-04 00:03:56:356"}
{"level":"info","message":"✅ Neo4j schema initialized successfully","timestamp":"2025-06-04 00:03:56:356"}
{"level":"info","message":"Initializing Weaviate schemas...","timestamp":"2025-06-04 00:03:56:356"}
{"error":"usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","level":"error","message":"Failed to create schema: Supplement","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 00:03:56:356"}
{"error":"usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","level":"error","message":"❌ Failed to initialize Weaviate schemas","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 00:03:56:356"}
{"level":"warn","message":"⚠️ Failed to connect to Weaviate, continuing without vector database: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 00:03:56:356"}
{"level":"info","message":"✅ Weaviate connected successfully","timestamp":"2025-06-04 00:03:56:356"}
{"level":"info","message":"Connecting to Redis cache...","timestamp":"2025-06-04 00:03:56:356"}
{"level":"info","message":"Redis client connected","timestamp":"2025-06-04 00:03:56:356"}
{"level":"info","message":"✅ Neo4j connected successfully","timestamp":"2025-06-04 00:03:56:356"}
{"level":"info","message":"Connecting to Weaviate vector database...","timestamp":"2025-06-04 00:03:56:356"}
{"level":"info","message":"Redis client ready","timestamp":"2025-06-04 00:03:56:356"}
{"level":"info","message":"✅ MongoDB connection established successfully","timestamp":"2025-06-04 00:03:56:356"}
{"level":"info","message":"✅ Weaviate connection established successfully","timestamp":"2025-06-04 00:03:56:356"}
{"level":"info","message":"✅ MongoDB connected successfully","timestamp":"2025-06-04 00:03:56:356"}
{"level":"info","message":"🚀 All databases connected successfully","timestamp":"2025-06-04 00:03:56:356"}
{"level":"info","message":"🚀 Server running on port 3000","timestamp":"2025-06-04 00:03:56:356"}
{"level":"info","message":"📊 Environment: development","timestamp":"2025-06-04 00:03:56:356"}
{"level":"info","message":"🔗 API URL: http://localhost:3000/api","timestamp":"2025-06-04 00:03:56:356"}
{"level":"info","message":"🔌 AG-UI WebSocket: ws://localhost:3000/agui/ws","timestamp":"2025-06-04 00:03:56:356"}
{"level":"info","message":"💚 Health check: http://localhost:3000/health","timestamp":"2025-06-04 00:03:56:356"}
{"level":"info","message":"✅ Redis connection established successfully","timestamp":"2025-06-04 00:03:56:356"}
{"level":"info","message":"✅ Redis connected successfully","timestamp":"2025-06-04 00:03:56:356"}
{"level":"info","message":"Connecting to MongoDB...","timestamp":"2025-06-04 00:03:56:356"}
{"level":"info","message":"Initializing Weaviate schemas...","timestamp":"2025-06-04 00:03:56:356"}
{"error":"usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","level":"error","message":"Failed to create schema: Supplement","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 00:03:56:356"}
{"error":"usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","level":"error","message":"❌ Failed to initialize Weaviate schemas","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 00:03:56:356"}
{"level":"warn","message":"⚠️ Failed to connect to Weaviate, continuing without vector database: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 00:03:56:356"}
{"level":"info","message":"✅ Weaviate connected successfully","timestamp":"2025-06-04 00:03:56:356"}
{"level":"info","message":"Connecting to Redis cache...","timestamp":"2025-06-04 00:03:56:356"}
{"level":"info","message":"Redis client connected","timestamp":"2025-06-04 00:03:56:356"}
{"level":"info","message":"Redis client ready","timestamp":"2025-06-04 00:03:56:356"}
{"level":"info","message":"✅ Redis connection established successfully","timestamp":"2025-06-04 00:03:56:356"}
{"level":"info","message":"✅ Redis connected successfully","timestamp":"2025-06-04 00:03:56:356"}
{"level":"info","message":"Connecting to MongoDB...","timestamp":"2025-06-04 00:03:56:356"}
{"level":"info","message":"✅ MongoDB connection established successfully","timestamp":"2025-06-04 00:03:56:356"}
{"level":"info","message":"✅ MongoDB connection established successfully","timestamp":"2025-06-04 00:03:56:356"}
{"level":"info","message":"Initializing database connections...","timestamp":"2025-06-04 00:04:01:41"}
{"level":"info","message":"Connecting to Neo4j database...","timestamp":"2025-06-04 00:04:01:41"}
{"level":"info","message":"✅ Neo4j connection established successfully","timestamp":"2025-06-04 00:04:01:41"}
{"level":"info","message":"Initializing Neo4j schema...","timestamp":"2025-06-04 00:04:01:41"}
{"level":"info","message":"✅ Neo4j schema initialized successfully","timestamp":"2025-06-04 00:04:01:41"}
{"level":"info","message":"✅ Neo4j connected successfully","timestamp":"2025-06-04 00:04:01:41"}
{"level":"info","message":"Connecting to Weaviate vector database...","timestamp":"2025-06-04 00:04:01:41"}
{"level":"info","message":"✅ Weaviate connection established successfully","timestamp":"2025-06-04 00:04:01:41"}
{"level":"info","message":"Initializing Weaviate schemas...","timestamp":"2025-06-04 00:04:01:41"}
{"error":"usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","level":"error","message":"Failed to create schema: Supplement","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 00:04:01:41"}
{"error":"usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","level":"error","message":"❌ Failed to initialize Weaviate schemas","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 00:04:01:41"}
{"level":"warn","message":"⚠️ Failed to connect to Weaviate, continuing without vector database: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 00:04:01:41"}
{"level":"info","message":"✅ Weaviate connected successfully","timestamp":"2025-06-04 00:04:01:41"}
{"level":"info","message":"Connecting to Redis cache...","timestamp":"2025-06-04 00:04:01:41"}
{"level":"info","message":"Redis client connected","timestamp":"2025-06-04 00:04:01:41"}
{"level":"info","message":"Redis client ready","timestamp":"2025-06-04 00:04:01:41"}
{"level":"info","message":"✅ Redis connection established successfully","timestamp":"2025-06-04 00:04:01:41"}
{"level":"info","message":"✅ Redis connected successfully","timestamp":"2025-06-04 00:04:01:41"}
{"level":"info","message":"Connecting to MongoDB...","timestamp":"2025-06-04 00:04:01:41"}
{"level":"info","message":"✅ MongoDB connection established successfully","timestamp":"2025-06-04 00:04:01:41"}
{"level":"info","message":"✅ MongoDB connected successfully","timestamp":"2025-06-04 00:04:01:41"}
{"level":"info","message":"🚀 All databases connected successfully","timestamp":"2025-06-04 00:04:01:41"}
{"level":"info","message":"🚀 Server running on port 3001","timestamp":"2025-06-04 00:04:01:41"}
{"level":"info","message":"📊 Environment: development","timestamp":"2025-06-04 00:04:01:41"}
{"level":"info","message":"🔗 API URL: http://localhost:3001/api","timestamp":"2025-06-04 00:04:01:41"}
{"level":"info","message":"🔌 AG-UI WebSocket: ws://localhost:3001/agui/ws","timestamp":"2025-06-04 00:04:01:41"}
{"level":"info","message":"💚 Health check: http://localhost:3001/health","timestamp":"2025-06-04 00:04:01:41"}
{"level":"info","message":"AG-UI WebSocket connection established: 77543004-c179-4f25-81c3-add26191fd3b","timestamp":"2025-06-04 00:04:02:42"}
{"level":"info","message":"AG-UI WebSocket connection established: 63cc952c-98e6-4cab-b18d-2417fccb069e","timestamp":"2025-06-04 00:04:02:42"}
{"level":"info","message":"AG-UI WebSocket connection established: 7687dccd-94fb-4149-8103-ed92e2d82cea","timestamp":"2025-06-04 00:04:02:42"}
{"level":"info","message":"📴 Received SIGTERM. Starting graceful shutdown...","timestamp":"2025-06-04 00:04:05:45"}
{"level":"info","message":"📴 Received SIGTERM. Starting graceful shutdown...","timestamp":"2025-06-04 00:04:05:45"}
{"level":"info","message":"Initializing database connections...","timestamp":"2025-06-04 00:04:07:47"}
{"level":"info","message":"Connecting to Neo4j database...","timestamp":"2025-06-04 00:04:07:47"}
{"level":"info","message":"Initializing database connections...","timestamp":"2025-06-04 00:04:07:47"}
{"level":"info","message":"Connecting to Neo4j database...","timestamp":"2025-06-04 00:04:07:47"}
{"level":"info","message":"✅ Neo4j connection established successfully","timestamp":"2025-06-04 00:04:07:47"}
{"level":"info","message":"✅ Neo4j connection established successfully","timestamp":"2025-06-04 00:04:07:47"}
{"level":"info","message":"Initializing Neo4j schema...","timestamp":"2025-06-04 00:04:07:47"}
{"level":"info","message":"Initializing Neo4j schema...","timestamp":"2025-06-04 00:04:07:47"}
{"level":"info","message":"Initializing database connections...","timestamp":"2025-06-04 00:04:07:47"}
{"level":"info","message":"Connecting to Neo4j database...","timestamp":"2025-06-04 00:04:07:47"}
{"level":"info","message":"✅ Neo4j connection established successfully","timestamp":"2025-06-04 00:04:07:47"}
{"level":"info","message":"Initializing Neo4j schema...","timestamp":"2025-06-04 00:04:07:47"}
{"level":"info","message":"✅ Neo4j schema initialized successfully","timestamp":"2025-06-04 00:04:07:47"}
{"level":"info","message":"✅ Neo4j schema initialized successfully","timestamp":"2025-06-04 00:04:07:47"}
{"level":"info","message":"✅ Neo4j connected successfully","timestamp":"2025-06-04 00:04:07:47"}
{"level":"info","message":"Connecting to Weaviate vector database...","timestamp":"2025-06-04 00:04:07:47"}
{"level":"info","message":"✅ Neo4j connected successfully","timestamp":"2025-06-04 00:04:07:47"}
{"level":"info","message":"Connecting to Weaviate vector database...","timestamp":"2025-06-04 00:04:07:47"}
{"level":"info","message":"✅ Weaviate connection established successfully","timestamp":"2025-06-04 00:04:07:47"}
{"level":"info","message":"✅ Weaviate connection established successfully","timestamp":"2025-06-04 00:04:07:47"}
{"level":"info","message":"✅ Neo4j schema initialized successfully","timestamp":"2025-06-04 00:04:07:47"}
{"level":"info","message":"Initializing Weaviate schemas...","timestamp":"2025-06-04 00:04:07:47"}
{"error":"usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","level":"error","message":"Failed to create schema: Supplement","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 00:04:07:47"}
{"error":"usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","level":"error","message":"❌ Failed to initialize Weaviate schemas","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 00:04:07:47"}
{"level":"warn","message":"⚠️ Failed to connect to Weaviate, continuing without vector database: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 00:04:07:47"}
{"level":"info","message":"✅ Weaviate connected successfully","timestamp":"2025-06-04 00:04:07:47"}
{"level":"info","message":"Connecting to Redis cache...","timestamp":"2025-06-04 00:04:07:47"}
{"level":"info","message":"Redis client connected","timestamp":"2025-06-04 00:04:07:47"}
{"level":"info","message":"Initializing Weaviate schemas...","timestamp":"2025-06-04 00:04:07:47"}
{"error":"usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","level":"error","message":"Failed to create schema: Supplement","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 00:04:07:47"}
{"error":"usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","level":"error","message":"❌ Failed to initialize Weaviate schemas","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 00:04:07:47"}
{"level":"warn","message":"⚠️ Failed to connect to Weaviate, continuing without vector database: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 00:04:07:47"}
{"level":"info","message":"✅ Weaviate connected successfully","timestamp":"2025-06-04 00:04:07:47"}
{"level":"info","message":"Connecting to Redis cache...","timestamp":"2025-06-04 00:04:07:47"}
{"level":"info","message":"Redis client ready","timestamp":"2025-06-04 00:04:07:47"}
{"level":"info","message":"Redis client connected","timestamp":"2025-06-04 00:04:07:47"}
{"level":"info","message":"✅ Neo4j connected successfully","timestamp":"2025-06-04 00:04:07:47"}
{"level":"info","message":"Connecting to Weaviate vector database...","timestamp":"2025-06-04 00:04:07:47"}
{"level":"info","message":"Redis client ready","timestamp":"2025-06-04 00:04:07:47"}
{"level":"info","message":"✅ Weaviate connection established successfully","timestamp":"2025-06-04 00:04:07:47"}
{"level":"info","message":"✅ Redis connection established successfully","timestamp":"2025-06-04 00:04:07:47"}
{"level":"info","message":"✅ Redis connected successfully","timestamp":"2025-06-04 00:04:07:47"}
{"level":"info","message":"Connecting to MongoDB...","timestamp":"2025-06-04 00:04:07:47"}
{"level":"info","message":"Initializing Weaviate schemas...","timestamp":"2025-06-04 00:04:07:47"}
{"error":"usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","level":"error","message":"Failed to create schema: Supplement","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 00:04:07:47"}
{"error":"usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","level":"error","message":"❌ Failed to initialize Weaviate schemas","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 00:04:07:47"}
{"level":"warn","message":"⚠️ Failed to connect to Weaviate, continuing without vector database: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 00:04:07:47"}
{"level":"info","message":"✅ Weaviate connected successfully","timestamp":"2025-06-04 00:04:07:47"}
{"level":"info","message":"Connecting to Redis cache...","timestamp":"2025-06-04 00:04:07:47"}
{"level":"info","message":"✅ Redis connection established successfully","timestamp":"2025-06-04 00:04:07:47"}
{"level":"info","message":"✅ Redis connected successfully","timestamp":"2025-06-04 00:04:07:47"}
{"level":"info","message":"Connecting to MongoDB...","timestamp":"2025-06-04 00:04:07:47"}
{"level":"info","message":"Redis client connected","timestamp":"2025-06-04 00:04:07:47"}
{"level":"info","message":"Redis client ready","timestamp":"2025-06-04 00:04:07:47"}
{"level":"info","message":"✅ MongoDB connection established successfully","timestamp":"2025-06-04 00:04:07:47"}
{"level":"info","message":"✅ Redis connection established successfully","timestamp":"2025-06-04 00:04:07:47"}
{"level":"info","message":"✅ Redis connected successfully","timestamp":"2025-06-04 00:04:07:47"}
{"level":"info","message":"Connecting to MongoDB...","timestamp":"2025-06-04 00:04:07:47"}
{"level":"info","message":"✅ MongoDB connected successfully","timestamp":"2025-06-04 00:04:07:47"}
{"level":"info","message":"🚀 All databases connected successfully","timestamp":"2025-06-04 00:04:07:47"}
{"level":"info","message":"🚀 Server running on port 3000","timestamp":"2025-06-04 00:04:07:47"}
{"level":"info","message":"📊 Environment: development","timestamp":"2025-06-04 00:04:07:47"}
{"level":"info","message":"🔗 API URL: http://localhost:3000/api","timestamp":"2025-06-04 00:04:07:47"}
{"level":"info","message":"🔌 AG-UI WebSocket: ws://localhost:3000/agui/ws","timestamp":"2025-06-04 00:04:07:47"}
{"level":"info","message":"💚 Health check: http://localhost:3000/health","timestamp":"2025-06-04 00:04:07:47"}
{"level":"info","message":"✅ MongoDB connection established successfully","timestamp":"2025-06-04 00:04:07:47"}
{"level":"info","message":"✅ MongoDB connection established successfully","timestamp":"2025-06-04 00:04:07:47"}
{"level":"info","message":"Initializing database connections...","timestamp":"2025-06-04 00:04:11:411"}
{"level":"info","message":"Connecting to Neo4j database...","timestamp":"2025-06-04 00:04:11:411"}
{"level":"info","message":"✅ Neo4j connection established successfully","timestamp":"2025-06-04 00:04:11:411"}
{"level":"info","message":"Initializing Neo4j schema...","timestamp":"2025-06-04 00:04:11:411"}
{"level":"info","message":"✅ Neo4j schema initialized successfully","timestamp":"2025-06-04 00:04:11:411"}
{"level":"info","message":"✅ Neo4j connected successfully","timestamp":"2025-06-04 00:04:11:411"}
{"level":"info","message":"Connecting to Weaviate vector database...","timestamp":"2025-06-04 00:04:11:411"}
{"level":"info","message":"✅ Weaviate connection established successfully","timestamp":"2025-06-04 00:04:11:411"}
{"level":"info","message":"Initializing Weaviate schemas...","timestamp":"2025-06-04 00:04:11:411"}
{"error":"usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","level":"error","message":"Failed to create schema: Supplement","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 00:04:11:411"}
{"error":"usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","level":"error","message":"❌ Failed to initialize Weaviate schemas","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 00:04:11:411"}
{"level":"warn","message":"⚠️ Failed to connect to Weaviate, continuing without vector database: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 00:04:11:411"}
{"level":"info","message":"✅ Weaviate connected successfully","timestamp":"2025-06-04 00:04:11:411"}
{"level":"info","message":"Connecting to Redis cache...","timestamp":"2025-06-04 00:04:11:411"}
{"level":"info","message":"Redis client connected","timestamp":"2025-06-04 00:04:11:411"}
{"level":"info","message":"Redis client ready","timestamp":"2025-06-04 00:04:11:411"}
{"level":"info","message":"✅ Redis connection established successfully","timestamp":"2025-06-04 00:04:11:411"}
{"level":"info","message":"✅ Redis connected successfully","timestamp":"2025-06-04 00:04:11:411"}
{"level":"info","message":"Connecting to MongoDB...","timestamp":"2025-06-04 00:04:11:411"}
{"level":"info","message":"✅ MongoDB connection established successfully","timestamp":"2025-06-04 00:04:11:411"}
{"level":"info","message":"✅ MongoDB connected successfully","timestamp":"2025-06-04 00:04:11:411"}
{"level":"info","message":"🚀 All databases connected successfully","timestamp":"2025-06-04 00:04:11:411"}
{"level":"info","message":"🚀 Server running on port 3001","timestamp":"2025-06-04 00:04:11:411"}
{"level":"info","message":"📊 Environment: development","timestamp":"2025-06-04 00:04:11:411"}
{"level":"info","message":"🔗 API URL: http://localhost:3001/api","timestamp":"2025-06-04 00:04:11:411"}
{"level":"info","message":"🔌 AG-UI WebSocket: ws://localhost:3001/agui/ws","timestamp":"2025-06-04 00:04:11:411"}
{"level":"info","message":"💚 Health check: http://localhost:3001/health","timestamp":"2025-06-04 00:04:11:411"}
{"level":"info","message":"AG-UI WebSocket connection established: 3922e5cc-2d7d-452c-ac16-5a7f64ca48d8","timestamp":"2025-06-04 00:04:12:412"}
{"level":"info","message":"AG-UI WebSocket connection established: a1cb6a28-0509-4a96-a7e9-b2de729078c0","timestamp":"2025-06-04 00:04:12:412"}
{"level":"info","message":"AG-UI WebSocket connection established: 74e02068-be2e-47a6-ad58-483c913193c6","timestamp":"2025-06-04 00:04:12:412"}
{"level":"info","message":"📴 Received SIGTERM. Starting graceful shutdown...","timestamp":"2025-06-04 00:04:32:432"}
{"level":"info","message":"📴 Received SIGTERM. Starting graceful shutdown...","timestamp":"2025-06-04 00:04:32:432"}
{"level":"info","message":"Initializing database connections...","timestamp":"2025-06-04 00:04:33:433"}
{"level":"info","message":"Connecting to Neo4j database...","timestamp":"2025-06-04 00:04:33:433"}
{"level":"info","message":"✅ Neo4j connection established successfully","timestamp":"2025-06-04 00:04:33:433"}
{"level":"info","message":"Initializing Neo4j schema...","timestamp":"2025-06-04 00:04:33:433"}
{"level":"info","message":"Initializing database connections...","timestamp":"2025-06-04 00:04:33:433"}
{"level":"info","message":"Connecting to Neo4j database...","timestamp":"2025-06-04 00:04:33:433"}
{"level":"info","message":"✅ Neo4j connection established successfully","timestamp":"2025-06-04 00:04:33:433"}
{"level":"info","message":"Initializing database connections...","timestamp":"2025-06-04 00:04:33:433"}
{"level":"info","message":"Connecting to Neo4j database...","timestamp":"2025-06-04 00:04:33:433"}
{"level":"info","message":"Initializing Neo4j schema...","timestamp":"2025-06-04 00:04:33:433"}
{"level":"info","message":"✅ Neo4j connection established successfully","timestamp":"2025-06-04 00:04:33:433"}
{"level":"info","message":"Initializing Neo4j schema...","timestamp":"2025-06-04 00:04:33:433"}
{"level":"info","message":"✅ Neo4j schema initialized successfully","timestamp":"2025-06-04 00:04:33:433"}
{"level":"info","message":"✅ Neo4j connected successfully","timestamp":"2025-06-04 00:04:33:433"}
{"level":"info","message":"Connecting to Weaviate vector database...","timestamp":"2025-06-04 00:04:33:433"}
{"level":"info","message":"✅ Weaviate connection established successfully","timestamp":"2025-06-04 00:04:33:433"}
{"level":"info","message":"Initializing Weaviate schemas...","timestamp":"2025-06-04 00:04:33:433"}
{"error":"usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","level":"error","message":"Failed to create schema: Supplement","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 00:04:33:433"}
{"error":"usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","level":"error","message":"❌ Failed to initialize Weaviate schemas","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 00:04:33:433"}
{"level":"warn","message":"⚠️ Failed to connect to Weaviate, continuing without vector database: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 00:04:33:433"}
{"level":"info","message":"✅ Weaviate connected successfully","timestamp":"2025-06-04 00:04:33:433"}
{"level":"info","message":"Connecting to Redis cache...","timestamp":"2025-06-04 00:04:33:433"}
{"level":"info","message":"Redis client connected","timestamp":"2025-06-04 00:04:33:433"}
{"level":"info","message":"Redis client ready","timestamp":"2025-06-04 00:04:33:433"}
{"level":"info","message":"✅ Neo4j schema initialized successfully","timestamp":"2025-06-04 00:04:33:433"}
{"level":"info","message":"✅ Redis connection established successfully","timestamp":"2025-06-04 00:04:33:433"}
{"level":"info","message":"✅ Redis connected successfully","timestamp":"2025-06-04 00:04:33:433"}
{"level":"info","message":"Connecting to MongoDB...","timestamp":"2025-06-04 00:04:33:433"}
{"level":"info","message":"✅ Neo4j connected successfully","timestamp":"2025-06-04 00:04:33:433"}
{"level":"info","message":"Connecting to Weaviate vector database...","timestamp":"2025-06-04 00:04:33:433"}
{"level":"info","message":"✅ Neo4j schema initialized successfully","timestamp":"2025-06-04 00:04:33:433"}
{"level":"info","message":"✅ Weaviate connection established successfully","timestamp":"2025-06-04 00:04:33:433"}
{"level":"info","message":"✅ Neo4j connected successfully","timestamp":"2025-06-04 00:04:33:433"}
{"level":"info","message":"Connecting to Weaviate vector database...","timestamp":"2025-06-04 00:04:33:433"}
{"level":"info","message":"Initializing Weaviate schemas...","timestamp":"2025-06-04 00:04:33:433"}
{"error":"usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","level":"error","message":"Failed to create schema: Supplement","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 00:04:33:433"}
{"error":"usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","level":"error","message":"❌ Failed to initialize Weaviate schemas","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 00:04:33:433"}
{"level":"warn","message":"⚠️ Failed to connect to Weaviate, continuing without vector database: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 00:04:33:433"}
{"level":"info","message":"✅ Weaviate connected successfully","timestamp":"2025-06-04 00:04:33:433"}
{"level":"info","message":"Connecting to Redis cache...","timestamp":"2025-06-04 00:04:33:433"}
{"level":"info","message":"✅ Weaviate connection established successfully","timestamp":"2025-06-04 00:04:33:433"}
{"level":"info","message":"Redis client connected","timestamp":"2025-06-04 00:04:33:433"}
{"level":"info","message":"✅ MongoDB connection established successfully","timestamp":"2025-06-04 00:04:33:433"}
{"level":"info","message":"Redis client ready","timestamp":"2025-06-04 00:04:33:433"}
{"level":"info","message":"✅ MongoDB connected successfully","timestamp":"2025-06-04 00:04:33:433"}
{"level":"info","message":"🚀 All databases connected successfully","timestamp":"2025-06-04 00:04:33:433"}
{"level":"info","message":"🚀 Server running on port 3000","timestamp":"2025-06-04 00:04:33:433"}
{"level":"info","message":"📊 Environment: development","timestamp":"2025-06-04 00:04:33:433"}
{"level":"info","message":"🔗 API URL: http://localhost:3000/api","timestamp":"2025-06-04 00:04:33:433"}
{"level":"info","message":"🔌 AG-UI WebSocket: ws://localhost:3000/agui/ws","timestamp":"2025-06-04 00:04:33:433"}
{"level":"info","message":"💚 Health check: http://localhost:3000/health","timestamp":"2025-06-04 00:04:33:433"}
{"level":"info","message":"Initializing Weaviate schemas...","timestamp":"2025-06-04 00:04:33:433"}
{"error":"usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","level":"error","message":"Failed to create schema: Supplement","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 00:04:33:433"}
{"error":"usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","level":"error","message":"❌ Failed to initialize Weaviate schemas","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 00:04:33:433"}
{"level":"warn","message":"⚠️ Failed to connect to Weaviate, continuing without vector database: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 00:04:33:433"}
{"level":"info","message":"✅ Weaviate connected successfully","timestamp":"2025-06-04 00:04:33:433"}
{"level":"info","message":"Connecting to Redis cache...","timestamp":"2025-06-04 00:04:33:433"}
{"level":"info","message":"Redis client connected","timestamp":"2025-06-04 00:04:33:433"}
{"level":"info","message":"Redis client ready","timestamp":"2025-06-04 00:04:33:433"}
{"level":"info","message":"✅ Redis connection established successfully","timestamp":"2025-06-04 00:04:33:433"}
{"level":"info","message":"✅ Redis connected successfully","timestamp":"2025-06-04 00:04:33:433"}
{"level":"info","message":"Connecting to MongoDB...","timestamp":"2025-06-04 00:04:33:433"}
{"level":"info","message":"✅ Redis connection established successfully","timestamp":"2025-06-04 00:04:33:433"}
{"level":"info","message":"✅ Redis connected successfully","timestamp":"2025-06-04 00:04:33:433"}
{"level":"info","message":"Connecting to MongoDB...","timestamp":"2025-06-04 00:04:33:433"}
{"level":"info","message":"✅ MongoDB connection established successfully","timestamp":"2025-06-04 00:04:33:433"}
{"level":"info","message":"✅ MongoDB connection established successfully","timestamp":"2025-06-04 00:04:33:433"}
{"level":"info","message":"Initializing database connections...","timestamp":"2025-06-04 00:04:38:438"}
{"level":"info","message":"Connecting to Neo4j database...","timestamp":"2025-06-04 00:04:38:438"}
{"level":"info","message":"✅ Neo4j connection established successfully","timestamp":"2025-06-04 00:04:38:438"}
{"level":"info","message":"Initializing Neo4j schema...","timestamp":"2025-06-04 00:04:38:438"}
{"level":"info","message":"✅ Neo4j schema initialized successfully","timestamp":"2025-06-04 00:04:38:438"}
{"level":"info","message":"✅ Neo4j connected successfully","timestamp":"2025-06-04 00:04:38:438"}
{"level":"info","message":"Connecting to Weaviate vector database...","timestamp":"2025-06-04 00:04:38:438"}
{"level":"info","message":"✅ Weaviate connection established successfully","timestamp":"2025-06-04 00:04:38:438"}
{"level":"info","message":"Initializing Weaviate schemas...","timestamp":"2025-06-04 00:04:38:438"}
{"error":"usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","level":"error","message":"Failed to create schema: Supplement","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 00:04:38:438"}
{"error":"usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","level":"error","message":"❌ Failed to initialize Weaviate schemas","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 00:04:38:438"}
{"level":"warn","message":"⚠️ Failed to connect to Weaviate, continuing without vector database: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 00:04:38:438"}
{"level":"info","message":"✅ Weaviate connected successfully","timestamp":"2025-06-04 00:04:38:438"}
{"level":"info","message":"Connecting to Redis cache...","timestamp":"2025-06-04 00:04:38:438"}
{"level":"info","message":"Redis client connected","timestamp":"2025-06-04 00:04:38:438"}
{"level":"info","message":"Redis client ready","timestamp":"2025-06-04 00:04:38:438"}
{"level":"info","message":"✅ Redis connection established successfully","timestamp":"2025-06-04 00:04:38:438"}
{"level":"info","message":"✅ Redis connected successfully","timestamp":"2025-06-04 00:04:38:438"}
{"level":"info","message":"Connecting to MongoDB...","timestamp":"2025-06-04 00:04:38:438"}
{"level":"info","message":"✅ MongoDB connection established successfully","timestamp":"2025-06-04 00:04:38:438"}
{"level":"info","message":"✅ MongoDB connected successfully","timestamp":"2025-06-04 00:04:38:438"}
{"level":"info","message":"🚀 All databases connected successfully","timestamp":"2025-06-04 00:04:38:438"}
{"level":"info","message":"🚀 Server running on port 3001","timestamp":"2025-06-04 00:04:38:438"}
{"level":"info","message":"📊 Environment: development","timestamp":"2025-06-04 00:04:38:438"}
{"level":"info","message":"🔗 API URL: http://localhost:3001/api","timestamp":"2025-06-04 00:04:38:438"}
{"level":"info","message":"🔌 AG-UI WebSocket: ws://localhost:3001/agui/ws","timestamp":"2025-06-04 00:04:38:438"}
{"level":"info","message":"💚 Health check: http://localhost:3001/health","timestamp":"2025-06-04 00:04:38:438"}
{"level":"info","message":"AG-UI WebSocket connection established: 51e1a6e3-6e29-4ca7-8015-1a5f05a75342","timestamp":"2025-06-04 00:04:39:439"}
{"level":"info","message":"AG-UI WebSocket connection established: d3406bd6-a21c-4341-9542-ac6723a1a69a","timestamp":"2025-06-04 00:04:39:439"}
{"level":"info","message":"AG-UI WebSocket connection established: 23abbf4c-2c40-4fe8-aa1e-5a6805863047","timestamp":"2025-06-04 00:04:39:439"}
{"level":"info","message":"Starting Gemma analysis for supplement: magnaz i żelazo","timestamp":"2025-06-04 00:04:48:448"}
{"level":"warn","message":"Empty or undefined response from Gemma for magnaz i żelazo, using fallback","timestamp":"2025-06-04 00:04:48:448"}
{"level":"info","message":"Gemma analysis completed for magnaz i żelazo","timestamp":"2025-06-04 00:04:48:448"}
{"error":"LIMIT: Invalid input. '1.0' is not a valid value. Must be a non-negative integer.","level":"error","message":"Neo4j query failed","parameters":{"limit":1,"searchQuery":"*magnaz i żelazo*"},"query":"\n        CALL db.index.fulltext.queryNodes('supplement_search', $searchQuery) \n        YIELD node, score\n        RETURN node, score\n        UNION\n        CALL db.index.fulltext.queryNodes('ingredient_search', $searchQuery) \n        YIELD node, score\n        RETURN node, score\n        UNION\n        CALL db.index.fulltext.queryNodes('effect_search', $searchQuery) \n        YIELD node, score\n        RETURN node, score\n        UNION\n        CALL db.index.fulltext.queryNodes('study_search', $searchQuery) \n        YIELD node, score\n        RETURN node, score\n        ORDER BY score DESC\n        LIMIT $limit\n      ","stack":"Neo4jError: LIMIT: Invalid input. '1.0' is not a valid value. Must be a non-negative integer.\n\n    at captureStacktrace (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:624:17)\n    at new Result (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:112:23)\n    at Session._run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:224:16)\n    at Session.run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:188:27)\n    at Neo4jConnection.executeQuery (/home/<USER>/Suplementor/backend/src/config/neo4j.ts:64:36)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async GraphService.searchNodes (/home/<USER>/Suplementor/backend/src/services/GraphService.ts:283:18)\n    at async GraphService.updateSupplementKnowledge (/home/<USER>/Suplementor/backend/src/services/GraphService.ts:810:29)\n    at async SupplementResearchAgent.executeResearchPipeline (/home/<USER>/Suplementor/backend/src/agents/SupplementResearchAgent.ts:134:27)","timestamp":"2025-06-04 00:04:48:448"}
{"error":"LIMIT: Invalid input. '1.0' is not a valid value. Must be a non-negative integer.","level":"error","message":"Neo4j query failed","parameters":{"limit":1,"search":"magnaz i żelazo"},"query":"\n          MATCH (n) \n          WHERE n.name CONTAINS $search \n             OR n.description CONTAINS $search\n             OR n.title CONTAINS $search\n         AND (n:Supplement) RETURN n, 1 as score ORDER BY n.name LIMIT $limit","stack":"Neo4jError: LIMIT: Invalid input. '1.0' is not a valid value. Must be a non-negative integer.\n\n    at captureStacktrace (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:624:17)\n    at new Result (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:112:23)\n    at Session._run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:224:16)\n    at Session.run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:188:27)\n    at Neo4jConnection.executeQuery (/home/<USER>/Suplementor/backend/src/config/neo4j.ts:64:36)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async GraphService.searchNodes (/home/<USER>/Suplementor/backend/src/services/GraphService.ts:300:18)\n    at async GraphService.updateSupplementKnowledge (/home/<USER>/Suplementor/backend/src/services/GraphService.ts:810:29)\n    at async SupplementResearchAgent.executeResearchPipeline (/home/<USER>/Suplementor/backend/src/agents/SupplementResearchAgent.ts:134:27)","timestamp":"2025-06-04 00:04:49:449"}
{"error":"LIMIT: Invalid input. '1.0' is not a valid value. Must be a non-negative integer.","filters":{"limit":1,"nodeTypes":["Supplement"]},"level":"error","message":"Failed to search nodes","searchQuery":"magnaz i żelazo","stack":"Neo4jError: LIMIT: Invalid input. '1.0' is not a valid value. Must be a non-negative integer.\n\n    at captureStacktrace (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:624:17)\n    at new Result (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:112:23)\n    at Session._run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:224:16)\n    at Session.run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:188:27)\n    at Neo4jConnection.executeQuery (/home/<USER>/Suplementor/backend/src/config/neo4j.ts:64:36)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async GraphService.searchNodes (/home/<USER>/Suplementor/backend/src/services/GraphService.ts:300:18)\n    at async GraphService.updateSupplementKnowledge (/home/<USER>/Suplementor/backend/src/services/GraphService.ts:810:29)\n    at async SupplementResearchAgent.executeResearchPipeline (/home/<USER>/Suplementor/backend/src/agents/SupplementResearchAgent.ts:134:27)","timestamp":"2025-06-04 00:04:49:449"}
{"error":"Failed to search nodes","level":"error","message":"Failed to update supplement knowledge","stack":"Error: Failed to search nodes\n    at GraphService.searchNodes (/home/<USER>/Suplementor/backend/src/services/GraphService.ts:327:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async GraphService.updateSupplementKnowledge (/home/<USER>/Suplementor/backend/src/services/GraphService.ts:810:29)\n    at async SupplementResearchAgent.executeResearchPipeline (/home/<USER>/Suplementor/backend/src/agents/SupplementResearchAgent.ts:134:27)","supplementName":"magnaz i żelazo","timestamp":"2025-06-04 00:04:49:449"}
{"code":"DATABASE_ERROR","isOperational":true,"level":"error","message":"Error in supplement research pipeline: Failed to update supplement knowledge in graph","stack":"Error: Failed to update supplement knowledge in graph\n    at GraphService.updateSupplementKnowledge (/home/<USER>/Suplementor/backend/src/services/GraphService.ts:963:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async SupplementResearchAgent.executeResearchPipeline (/home/<USER>/Suplementor/backend/src/agents/SupplementResearchAgent.ts:134:27)","statusCode":500,"timestamp":"2025-06-04 00:04:49:449"}
{"code":"DATABASE_ERROR","isOperational":true,"level":"error","message":"Agent error for 23abbf4c-2c40-4fe8-aa1e-5a6805863047-4ae2a3d6-50b6-470b-b7cc-8f3432fb10ca: Failed to update supplement knowledge in graph","stack":"Error: Failed to update supplement knowledge in graph\n    at GraphService.updateSupplementKnowledge (/home/<USER>/Suplementor/backend/src/services/GraphService.ts:963:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async SupplementResearchAgent.executeResearchPipeline (/home/<USER>/Suplementor/backend/src/agents/SupplementResearchAgent.ts:134:27)","statusCode":500,"timestamp":"2025-06-04 00:04:49:449"}
{"level":"info","message":"📴 Received SIGTERM. Starting graceful shutdown...","timestamp":"2025-06-04 00:05:15:515"}
{"level":"info","message":"📴 Received SIGTERM. Starting graceful shutdown...","timestamp":"2025-06-04 00:05:15:515"}
{"level":"info","message":"Initializing database connections...","timestamp":"2025-06-04 00:05:17:517"}
{"level":"info","message":"Connecting to Neo4j database...","timestamp":"2025-06-04 00:05:17:517"}
{"level":"info","message":"✅ Neo4j connection established successfully","timestamp":"2025-06-04 00:05:17:517"}
{"level":"info","message":"Initializing database connections...","timestamp":"2025-06-04 00:05:17:517"}
{"level":"info","message":"Connecting to Neo4j database...","timestamp":"2025-06-04 00:05:17:517"}
{"level":"info","message":"Initializing database connections...","timestamp":"2025-06-04 00:05:17:517"}
{"level":"info","message":"Connecting to Neo4j database...","timestamp":"2025-06-04 00:05:17:517"}
{"level":"info","message":"✅ Neo4j connection established successfully","timestamp":"2025-06-04 00:05:17:517"}
{"level":"info","message":"✅ Neo4j connection established successfully","timestamp":"2025-06-04 00:05:17:517"}
{"level":"info","message":"Initializing database connections...","timestamp":"2025-06-04 00:05:21:521"}
{"level":"info","message":"Connecting to Neo4j database...","timestamp":"2025-06-04 00:05:21:521"}
{"level":"info","message":"✅ Neo4j connection established successfully","timestamp":"2025-06-04 00:05:21:521"}
{"level":"info","message":"Initializing database connections...","timestamp":"2025-06-04 00:05:44:544"}
{"level":"info","message":"Connecting to Neo4j database...","timestamp":"2025-06-04 00:05:44:544"}
{"level":"info","message":"✅ Neo4j connection established successfully","timestamp":"2025-06-04 00:05:44:544"}
{"level":"info","message":"Initializing Neo4j schema...","timestamp":"2025-06-04 00:05:44:544"}
{"level":"info","message":"Initializing database connections...","timestamp":"2025-06-04 00:05:44:544"}
{"level":"info","message":"Connecting to Neo4j database...","timestamp":"2025-06-04 00:05:44:544"}
{"level":"info","message":"Initializing database connections...","timestamp":"2025-06-04 00:05:44:544"}
{"level":"info","message":"Connecting to Neo4j database...","timestamp":"2025-06-04 00:05:44:544"}
{"level":"info","message":"Initializing database connections...","timestamp":"2025-06-04 00:05:44:544"}
{"level":"info","message":"Connecting to Neo4j database...","timestamp":"2025-06-04 00:05:44:544"}
{"level":"info","message":"✅ Neo4j connection established successfully","timestamp":"2025-06-04 00:05:44:544"}
{"level":"info","message":"✅ Neo4j connection established successfully","timestamp":"2025-06-04 00:05:44:544"}
{"level":"info","message":"Initializing Neo4j schema...","timestamp":"2025-06-04 00:05:44:544"}
{"level":"info","message":"✅ Neo4j connection established successfully","timestamp":"2025-06-04 00:05:44:544"}
{"level":"info","message":"Initializing Neo4j schema...","timestamp":"2025-06-04 00:05:44:544"}
{"level":"info","message":"Initializing Neo4j schema...","timestamp":"2025-06-04 00:05:44:544"}
{"level":"info","message":"✅ Neo4j schema initialized successfully","timestamp":"2025-06-04 00:05:44:544"}
{"level":"info","message":"✅ Neo4j connected successfully","timestamp":"2025-06-04 00:05:44:544"}
{"level":"info","message":"Connecting to Weaviate vector database...","timestamp":"2025-06-04 00:05:44:544"}
{"level":"info","message":"✅ Neo4j schema initialized successfully","timestamp":"2025-06-04 00:05:44:544"}
{"level":"info","message":"✅ Neo4j schema initialized successfully","timestamp":"2025-06-04 00:05:44:544"}
{"level":"info","message":"✅ Weaviate connection established successfully","timestamp":"2025-06-04 00:05:44:544"}
{"level":"info","message":"✅ Neo4j schema initialized successfully","timestamp":"2025-06-04 00:05:44:544"}
{"level":"info","message":"✅ Neo4j connected successfully","timestamp":"2025-06-04 00:05:44:544"}
{"level":"info","message":"Connecting to Weaviate vector database...","timestamp":"2025-06-04 00:05:44:544"}
{"level":"info","message":"✅ Neo4j connected successfully","timestamp":"2025-06-04 00:05:44:544"}
{"level":"info","message":"Connecting to Weaviate vector database...","timestamp":"2025-06-04 00:05:44:544"}
{"level":"info","message":"✅ Neo4j connected successfully","timestamp":"2025-06-04 00:05:44:544"}
{"level":"info","message":"Connecting to Weaviate vector database...","timestamp":"2025-06-04 00:05:44:544"}
{"level":"info","message":"✅ Weaviate connection established successfully","timestamp":"2025-06-04 00:05:44:544"}
{"level":"info","message":"Initializing Weaviate schemas...","timestamp":"2025-06-04 00:05:44:544"}
{"error":"usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","level":"error","message":"Failed to create schema: Supplement","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 00:05:44:544"}
{"error":"usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","level":"error","message":"❌ Failed to initialize Weaviate schemas","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 00:05:44:544"}
{"level":"warn","message":"⚠️ Failed to connect to Weaviate, continuing without vector database: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 00:05:44:544"}
{"level":"info","message":"✅ Weaviate connected successfully","timestamp":"2025-06-04 00:05:44:544"}
{"level":"info","message":"Connecting to Redis cache...","timestamp":"2025-06-04 00:05:44:544"}
{"level":"info","message":"✅ Weaviate connection established successfully","timestamp":"2025-06-04 00:05:44:544"}
{"level":"info","message":"✅ Weaviate connection established successfully","timestamp":"2025-06-04 00:05:44:544"}
{"level":"info","message":"Redis client connected","timestamp":"2025-06-04 00:05:44:544"}
{"level":"info","message":"Redis client ready","timestamp":"2025-06-04 00:05:44:544"}
{"level":"info","message":"Initializing Weaviate schemas...","timestamp":"2025-06-04 00:05:44:544"}
{"error":"usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","level":"error","message":"Failed to create schema: Supplement","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 00:05:44:544"}
{"error":"usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","level":"error","message":"❌ Failed to initialize Weaviate schemas","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 00:05:44:544"}
{"level":"warn","message":"⚠️ Failed to connect to Weaviate, continuing without vector database: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 00:05:44:544"}
{"level":"info","message":"✅ Weaviate connected successfully","timestamp":"2025-06-04 00:05:44:544"}
{"level":"info","message":"Connecting to Redis cache...","timestamp":"2025-06-04 00:05:44:544"}
{"level":"info","message":"Redis client connected","timestamp":"2025-06-04 00:05:44:544"}
{"level":"info","message":"Redis client ready","timestamp":"2025-06-04 00:05:44:544"}
{"level":"info","message":"Initializing Weaviate schemas...","timestamp":"2025-06-04 00:05:44:544"}
{"error":"usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","level":"error","message":"Failed to create schema: Supplement","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 00:05:44:544"}
{"error":"usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","level":"error","message":"❌ Failed to initialize Weaviate schemas","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 00:05:44:544"}
{"level":"warn","message":"⚠️ Failed to connect to Weaviate, continuing without vector database: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 00:05:44:544"}
{"level":"info","message":"✅ Weaviate connected successfully","timestamp":"2025-06-04 00:05:44:544"}
{"level":"info","message":"Connecting to Redis cache...","timestamp":"2025-06-04 00:05:44:544"}
{"level":"info","message":"Initializing Weaviate schemas...","timestamp":"2025-06-04 00:05:44:544"}
{"error":"usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","level":"error","message":"Failed to create schema: Supplement","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 00:05:44:544"}
{"error":"usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","level":"error","message":"❌ Failed to initialize Weaviate schemas","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 00:05:44:544"}
{"level":"warn","message":"⚠️ Failed to connect to Weaviate, continuing without vector database: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 00:05:44:544"}
{"level":"info","message":"✅ Weaviate connected successfully","timestamp":"2025-06-04 00:05:44:544"}
{"level":"info","message":"Connecting to Redis cache...","timestamp":"2025-06-04 00:05:44:544"}
{"level":"info","message":"Redis client connected","timestamp":"2025-06-04 00:05:44:544"}
{"level":"info","message":"Redis client connected","timestamp":"2025-06-04 00:05:44:544"}
{"level":"info","message":"Redis client ready","timestamp":"2025-06-04 00:05:44:544"}
{"level":"info","message":"Redis client ready","timestamp":"2025-06-04 00:05:44:544"}
{"level":"info","message":"✅ Redis connection established successfully","timestamp":"2025-06-04 00:05:44:544"}
{"level":"info","message":"✅ Redis connected successfully","timestamp":"2025-06-04 00:05:44:544"}
{"level":"info","message":"Connecting to MongoDB...","timestamp":"2025-06-04 00:05:44:544"}
{"level":"info","message":"✅ Redis connection established successfully","timestamp":"2025-06-04 00:05:44:544"}
{"level":"info","message":"✅ Redis connected successfully","timestamp":"2025-06-04 00:05:44:544"}
{"level":"info","message":"Connecting to MongoDB...","timestamp":"2025-06-04 00:05:44:544"}
{"level":"info","message":"✅ Redis connection established successfully","timestamp":"2025-06-04 00:05:44:544"}
{"level":"info","message":"✅ Redis connected successfully","timestamp":"2025-06-04 00:05:44:544"}
{"level":"info","message":"Connecting to MongoDB...","timestamp":"2025-06-04 00:05:44:544"}
{"level":"info","message":"✅ Redis connection established successfully","timestamp":"2025-06-04 00:05:44:544"}
{"level":"info","message":"✅ Redis connected successfully","timestamp":"2025-06-04 00:05:44:544"}
{"level":"info","message":"Connecting to MongoDB...","timestamp":"2025-06-04 00:05:44:544"}
{"level":"info","message":"✅ MongoDB connection established successfully","timestamp":"2025-06-04 00:05:44:544"}
{"level":"info","message":"✅ MongoDB connected successfully","timestamp":"2025-06-04 00:05:44:544"}
{"level":"info","message":"🚀 All databases connected successfully","timestamp":"2025-06-04 00:05:44:544"}
{"level":"info","message":"🚀 Server running on port 3001","timestamp":"2025-06-04 00:05:44:544"}
{"level":"info","message":"📊 Environment: development","timestamp":"2025-06-04 00:05:44:544"}
{"level":"info","message":"🔗 API URL: http://localhost:3001/api","timestamp":"2025-06-04 00:05:44:544"}
{"level":"info","message":"🔌 AG-UI WebSocket: ws://localhost:3001/agui/ws","timestamp":"2025-06-04 00:05:44:544"}
{"level":"info","message":"💚 Health check: http://localhost:3001/health","timestamp":"2025-06-04 00:05:44:544"}
{"level":"info","message":"✅ MongoDB connection established successfully","timestamp":"2025-06-04 00:05:44:544"}
{"level":"info","message":"✅ MongoDB connection established successfully","timestamp":"2025-06-04 00:05:44:544"}
{"level":"info","message":"✅ MongoDB connection established successfully","timestamp":"2025-06-04 00:05:44:544"}
{"level":"info","message":"✅ MongoDB connected successfully","timestamp":"2025-06-04 00:05:44:544"}
{"level":"info","message":"🚀 All databases connected successfully","timestamp":"2025-06-04 00:05:44:544"}
{"level":"info","message":"🚀 Server running on port 3000","timestamp":"2025-06-04 00:05:44:544"}
{"level":"info","message":"📊 Environment: development","timestamp":"2025-06-04 00:05:44:544"}
{"level":"info","message":"🔗 API URL: http://localhost:3000/api","timestamp":"2025-06-04 00:05:44:544"}
{"level":"info","message":"🔌 AG-UI WebSocket: ws://localhost:3000/agui/ws","timestamp":"2025-06-04 00:05:44:544"}
{"level":"info","message":"💚 Health check: http://localhost:3000/health","timestamp":"2025-06-04 00:05:44:544"}
{"level":"info","message":"AG-UI WebSocket connection established: 94cfa245-245b-4528-8bb1-84c09600504c","timestamp":"2025-06-04 00:05:44:544"}
{"level":"info","message":"AG-UI WebSocket connection established: d06f8408-94b5-419f-8639-20ea31d37149","timestamp":"2025-06-04 00:05:48:548"}
{"level":"info","message":"AG-UI WebSocket connection established: 5f2fbbdd-ae1e-4b19-be32-0adfca58905b","timestamp":"2025-06-04 00:06:10:610"}
{"level":"info","message":"AG-UI WebSocket connection closed: 5f2fbbdd-ae1e-4b19-be32-0adfca58905b","timestamp":"2025-06-04 00:06:10:610"}
{"level":"info","message":"AG-UI WebSocket connection established: cc03b53a-e63d-43df-b285-d7ad78e4b150","timestamp":"2025-06-04 00:06:10:610"}
{"level":"info","message":"AG-UI WebSocket connection established: 32902675-7b7b-4239-9b58-9f83dd0e5eb5","timestamp":"2025-06-04 00:06:11:611"}
{"level":"info","message":"AG-UI WebSocket connection closed: cc03b53a-e63d-43df-b285-d7ad78e4b150","timestamp":"2025-06-04 00:06:18:618"}
{"level":"info","message":"AG-UI WebSocket connection closed: 32902675-7b7b-4239-9b58-9f83dd0e5eb5","timestamp":"2025-06-04 00:06:18:618"}
{"level":"info","message":"AG-UI WebSocket connection closed: d06f8408-94b5-419f-8639-20ea31d37149","timestamp":"2025-06-04 00:06:18:618"}
{"level":"info","message":"AG-UI WebSocket connection established: 461a8f32-d565-451b-a043-a1a3adf86f37","timestamp":"2025-06-04 00:06:18:618"}
{"level":"info","message":"AG-UI WebSocket connection established: 398620da-d06e-4cac-9038-63eab89a87fd","timestamp":"2025-06-04 00:06:18:618"}
{"level":"info","message":"AG-UI WebSocket connection established: eb34f5ce-72fd-467a-bc93-1348a845c337","timestamp":"2025-06-04 00:06:18:618"}
{"level":"info","message":"AG-UI WebSocket connection closed: 398620da-d06e-4cac-9038-63eab89a87fd","timestamp":"2025-06-04 00:06:18:618"}
{"level":"info","message":"AG-UI WebSocket connection established: 98b4a112-f40d-4176-a039-4bfc7286e4c4","timestamp":"2025-06-04 00:06:19:619"}
{"level":"info","message":"AG-UI WebSocket connection established: 34d65f9d-d1f3-4f9d-a8a4-01d064b6f0e1","timestamp":"2025-06-04 00:06:20:620"}
{"level":"info","message":"Starting Gemma analysis for supplement: tyrozyna","timestamp":"2025-06-04 00:06:22:622"}
{"level":"warn","message":"Empty or undefined response from Gemma for tyrozyna, using fallback","timestamp":"2025-06-04 00:06:22:622"}
{"level":"info","message":"Gemma analysis completed for tyrozyna","timestamp":"2025-06-04 00:06:22:622"}
{"error":"LIMIT: Invalid input. '1.0' is not a valid value. Must be a non-negative integer.","level":"error","message":"Neo4j query failed","parameters":{"limit":1,"searchQuery":"*tyrozyna*"},"query":"\n        CALL db.index.fulltext.queryNodes('supplement_search', $searchQuery) \n        YIELD node, score\n        RETURN node, score\n        UNION\n        CALL db.index.fulltext.queryNodes('ingredient_search', $searchQuery) \n        YIELD node, score\n        RETURN node, score\n        UNION\n        CALL db.index.fulltext.queryNodes('effect_search', $searchQuery) \n        YIELD node, score\n        RETURN node, score\n        UNION\n        CALL db.index.fulltext.queryNodes('study_search', $searchQuery) \n        YIELD node, score\n        RETURN node, score\n        ORDER BY score DESC\n        LIMIT $limit\n      ","stack":"Neo4jError: LIMIT: Invalid input. '1.0' is not a valid value. Must be a non-negative integer.\n\n    at captureStacktrace (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:624:17)\n    at new Result (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:112:23)\n    at Session._run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:224:16)\n    at Session.run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:188:27)\n    at Neo4jConnection.executeQuery (/home/<USER>/Suplementor/backend/src/config/neo4j.ts:87:36)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async GraphService.searchNodes (/home/<USER>/Suplementor/backend/src/services/GraphService.ts:283:18)\n    at async GraphService.updateSupplementKnowledge (/home/<USER>/Suplementor/backend/src/services/GraphService.ts:810:29)\n    at async SupplementResearchAgent.executeResearchPipeline (/home/<USER>/Suplementor/backend/src/agents/SupplementResearchAgent.ts:134:27)","timestamp":"2025-06-04 00:06:22:622"}
{"error":"LIMIT: Invalid input. '1.0' is not a valid value. Must be a non-negative integer.","level":"error","message":"Neo4j query failed","parameters":{"limit":1,"search":"tyrozyna"},"query":"\n          MATCH (n) \n          WHERE n.name CONTAINS $search \n             OR n.description CONTAINS $search\n             OR n.title CONTAINS $search\n         AND (n:Supplement) RETURN n, 1 as score ORDER BY n.name LIMIT $limit","stack":"Neo4jError: LIMIT: Invalid input. '1.0' is not a valid value. Must be a non-negative integer.\n\n    at captureStacktrace (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:624:17)\n    at new Result (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:112:23)\n    at Session._run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:224:16)\n    at Session.run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:188:27)\n    at Neo4jConnection.executeQuery (/home/<USER>/Suplementor/backend/src/config/neo4j.ts:87:36)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async GraphService.searchNodes (/home/<USER>/Suplementor/backend/src/services/GraphService.ts:300:18)\n    at async GraphService.updateSupplementKnowledge (/home/<USER>/Suplementor/backend/src/services/GraphService.ts:810:29)\n    at async SupplementResearchAgent.executeResearchPipeline (/home/<USER>/Suplementor/backend/src/agents/SupplementResearchAgent.ts:134:27)","timestamp":"2025-06-04 00:06:22:622"}
{"error":"LIMIT: Invalid input. '1.0' is not a valid value. Must be a non-negative integer.","filters":{"limit":1,"nodeTypes":["Supplement"]},"level":"error","message":"Failed to search nodes","searchQuery":"tyrozyna","stack":"Neo4jError: LIMIT: Invalid input. '1.0' is not a valid value. Must be a non-negative integer.\n\n    at captureStacktrace (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:624:17)\n    at new Result (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:112:23)\n    at Session._run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:224:16)\n    at Session.run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:188:27)\n    at Neo4jConnection.executeQuery (/home/<USER>/Suplementor/backend/src/config/neo4j.ts:87:36)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async GraphService.searchNodes (/home/<USER>/Suplementor/backend/src/services/GraphService.ts:300:18)\n    at async GraphService.updateSupplementKnowledge (/home/<USER>/Suplementor/backend/src/services/GraphService.ts:810:29)\n    at async SupplementResearchAgent.executeResearchPipeline (/home/<USER>/Suplementor/backend/src/agents/SupplementResearchAgent.ts:134:27)","timestamp":"2025-06-04 00:06:22:622"}
{"error":"Failed to search nodes","level":"error","message":"Failed to update supplement knowledge","stack":"Error: Failed to search nodes\n    at GraphService.searchNodes (/home/<USER>/Suplementor/backend/src/services/GraphService.ts:327:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async GraphService.updateSupplementKnowledge (/home/<USER>/Suplementor/backend/src/services/GraphService.ts:810:29)\n    at async SupplementResearchAgent.executeResearchPipeline (/home/<USER>/Suplementor/backend/src/agents/SupplementResearchAgent.ts:134:27)","supplementName":"tyrozyna","timestamp":"2025-06-04 00:06:22:622"}
{"code":"DATABASE_ERROR","isOperational":true,"level":"error","message":"Error in supplement research pipeline: Failed to update supplement knowledge in graph","stack":"Error: Failed to update supplement knowledge in graph\n    at GraphService.updateSupplementKnowledge (/home/<USER>/Suplementor/backend/src/services/GraphService.ts:963:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async SupplementResearchAgent.executeResearchPipeline (/home/<USER>/Suplementor/backend/src/agents/SupplementResearchAgent.ts:134:27)","statusCode":500,"timestamp":"2025-06-04 00:06:22:622"}
{"code":"DATABASE_ERROR","isOperational":true,"level":"error","message":"Agent error for d06f8408-94b5-419f-8639-20ea31d37149-6792fd4d-eb4b-4082-ba19-40e222d018f0: Failed to update supplement knowledge in graph","stack":"Error: Failed to update supplement knowledge in graph\n    at GraphService.updateSupplementKnowledge (/home/<USER>/Suplementor/backend/src/services/GraphService.ts:963:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async SupplementResearchAgent.executeResearchPipeline (/home/<USER>/Suplementor/backend/src/agents/SupplementResearchAgent.ts:134:27)","statusCode":500,"timestamp":"2025-06-04 00:06:22:622"}
{"level":"info","message":"AG-UI WebSocket connection established: 920f2ee3-1c46-4a6a-94d6-161732332d96","timestamp":"2025-06-04 00:06:23:623"}
{"level":"info","message":"AG-UI WebSocket connection closed: 920f2ee3-1c46-4a6a-94d6-161732332d96","timestamp":"2025-06-04 00:06:23:623"}
{"level":"info","message":"AG-UI WebSocket connection established: 2e80a59b-49f4-4353-9117-6f689d01112c","timestamp":"2025-06-04 00:06:23:623"}
{"level":"info","message":"Starting Gemma analysis for supplement: tyrozyna","timestamp":"2025-06-04 00:06:24:624"}
{"level":"warn","message":"Empty or undefined response from Gemma for tyrozyna, using fallback","timestamp":"2025-06-04 00:06:24:624"}
{"level":"info","message":"Gemma analysis completed for tyrozyna","timestamp":"2025-06-04 00:06:24:624"}
{"error":"LIMIT: Invalid input. '1.0' is not a valid value. Must be a non-negative integer.","level":"error","message":"Neo4j query failed","parameters":{"limit":1,"searchQuery":"*tyrozyna*"},"query":"\n        CALL db.index.fulltext.queryNodes('supplement_search', $searchQuery) \n        YIELD node, score\n        RETURN node, score\n        UNION\n        CALL db.index.fulltext.queryNodes('ingredient_search', $searchQuery) \n        YIELD node, score\n        RETURN node, score\n        UNION\n        CALL db.index.fulltext.queryNodes('effect_search', $searchQuery) \n        YIELD node, score\n        RETURN node, score\n        UNION\n        CALL db.index.fulltext.queryNodes('study_search', $searchQuery) \n        YIELD node, score\n        RETURN node, score\n        ORDER BY score DESC\n        LIMIT $limit\n      ","stack":"Neo4jError: LIMIT: Invalid input. '1.0' is not a valid value. Must be a non-negative integer.\n\n    at captureStacktrace (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:624:17)\n    at new Result (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:112:23)\n    at Session._run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:224:16)\n    at Session.run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:188:27)\n    at Neo4jConnection.executeQuery (/home/<USER>/Suplementor/backend/src/config/neo4j.ts:87:36)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async GraphService.searchNodes (/home/<USER>/Suplementor/backend/src/services/GraphService.ts:283:18)\n    at async GraphService.updateSupplementKnowledge (/home/<USER>/Suplementor/backend/src/services/GraphService.ts:810:29)\n    at async SupplementResearchAgent.executeResearchPipeline (/home/<USER>/Suplementor/backend/src/agents/SupplementResearchAgent.ts:134:27)","timestamp":"2025-06-04 00:06:24:624"}
{"error":"LIMIT: Invalid input. '1.0' is not a valid value. Must be a non-negative integer.","level":"error","message":"Neo4j query failed","parameters":{"limit":1,"search":"tyrozyna"},"query":"\n          MATCH (n) \n          WHERE n.name CONTAINS $search \n             OR n.description CONTAINS $search\n             OR n.title CONTAINS $search\n         AND (n:Supplement) RETURN n, 1 as score ORDER BY n.name LIMIT $limit","stack":"Neo4jError: LIMIT: Invalid input. '1.0' is not a valid value. Must be a non-negative integer.\n\n    at captureStacktrace (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:624:17)\n    at new Result (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:112:23)\n    at Session._run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:224:16)\n    at Session.run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:188:27)\n    at Neo4jConnection.executeQuery (/home/<USER>/Suplementor/backend/src/config/neo4j.ts:87:36)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async GraphService.searchNodes (/home/<USER>/Suplementor/backend/src/services/GraphService.ts:300:18)\n    at async GraphService.updateSupplementKnowledge (/home/<USER>/Suplementor/backend/src/services/GraphService.ts:810:29)\n    at async SupplementResearchAgent.executeResearchPipeline (/home/<USER>/Suplementor/backend/src/agents/SupplementResearchAgent.ts:134:27)","timestamp":"2025-06-04 00:06:24:624"}
{"error":"LIMIT: Invalid input. '1.0' is not a valid value. Must be a non-negative integer.","filters":{"limit":1,"nodeTypes":["Supplement"]},"level":"error","message":"Failed to search nodes","searchQuery":"tyrozyna","stack":"Neo4jError: LIMIT: Invalid input. '1.0' is not a valid value. Must be a non-negative integer.\n\n    at captureStacktrace (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:624:17)\n    at new Result (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/result.js:112:23)\n    at Session._run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:224:16)\n    at Session.run (/home/<USER>/Suplementor/backend/node_modules/neo4j-driver-core/lib/session.js:188:27)\n    at Neo4jConnection.executeQuery (/home/<USER>/Suplementor/backend/src/config/neo4j.ts:87:36)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async GraphService.searchNodes (/home/<USER>/Suplementor/backend/src/services/GraphService.ts:300:18)\n    at async GraphService.updateSupplementKnowledge (/home/<USER>/Suplementor/backend/src/services/GraphService.ts:810:29)\n    at async SupplementResearchAgent.executeResearchPipeline (/home/<USER>/Suplementor/backend/src/agents/SupplementResearchAgent.ts:134:27)","timestamp":"2025-06-04 00:06:24:624"}
{"error":"Failed to search nodes","level":"error","message":"Failed to update supplement knowledge","stack":"Error: Failed to search nodes\n    at GraphService.searchNodes (/home/<USER>/Suplementor/backend/src/services/GraphService.ts:327:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async GraphService.updateSupplementKnowledge (/home/<USER>/Suplementor/backend/src/services/GraphService.ts:810:29)\n    at async SupplementResearchAgent.executeResearchPipeline (/home/<USER>/Suplementor/backend/src/agents/SupplementResearchAgent.ts:134:27)","supplementName":"tyrozyna","timestamp":"2025-06-04 00:06:24:624"}
{"code":"DATABASE_ERROR","isOperational":true,"level":"error","message":"Error in supplement research pipeline: Failed to update supplement knowledge in graph","stack":"Error: Failed to update supplement knowledge in graph\n    at GraphService.updateSupplementKnowledge (/home/<USER>/Suplementor/backend/src/services/GraphService.ts:963:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async SupplementResearchAgent.executeResearchPipeline (/home/<USER>/Suplementor/backend/src/agents/SupplementResearchAgent.ts:134:27)","statusCode":500,"timestamp":"2025-06-04 00:06:24:624"}
{"code":"DATABASE_ERROR","isOperational":true,"level":"error","message":"Agent error for 32902675-7b7b-4239-9b58-9f83dd0e5eb5-e92a63b9-2948-422f-96e4-2f4882224230: Failed to update supplement knowledge in graph","stack":"Error: Failed to update supplement knowledge in graph\n    at GraphService.updateSupplementKnowledge (/home/<USER>/Suplementor/backend/src/services/GraphService.ts:963:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async SupplementResearchAgent.executeResearchPipeline (/home/<USER>/Suplementor/backend/src/agents/SupplementResearchAgent.ts:134:27)","statusCode":500,"timestamp":"2025-06-04 00:06:24:624"}
{"level":"info","message":"AG-UI WebSocket connection established: d810e5e4-aa48-4e21-b4cc-aba9b6b9a213","timestamp":"2025-06-04 00:06:24:624"}
{"level":"info","message":"AG-UI WebSocket connection closed: d810e5e4-aa48-4e21-b4cc-aba9b6b9a213","timestamp":"2025-06-04 00:06:24:624"}
{"level":"info","message":"AG-UI WebSocket connection closed: 461a8f32-d565-451b-a043-a1a3adf86f37","timestamp":"2025-06-04 00:06:26:626"}
{"level":"info","message":"AG-UI WebSocket connection closed: 2e80a59b-49f4-4353-9117-6f689d01112c","timestamp":"2025-06-04 00:06:26:626"}
{"level":"info","message":"AG-UI WebSocket connection closed: 98b4a112-f40d-4176-a039-4bfc7286e4c4","timestamp":"2025-06-04 00:06:26:626"}
{"level":"info","message":"AG-UI WebSocket connection established: 623f6d6d-c7c4-4ff5-b432-0031c67f45b9","timestamp":"2025-06-04 00:06:26:626"}
{"level":"info","message":"AG-UI WebSocket connection closed: 623f6d6d-c7c4-4ff5-b432-0031c67f45b9","timestamp":"2025-06-04 00:06:26:626"}
{"level":"info","message":"AG-UI WebSocket connection established: 0ef190d2-a77b-44e7-a080-abec4276cf00","timestamp":"2025-06-04 00:06:26:626"}
{"level":"info","message":"AG-UI WebSocket connection closed: eb34f5ce-72fd-467a-bc93-1348a845c337","timestamp":"2025-06-04 00:06:27:627"}
{"level":"info","message":"AG-UI WebSocket connection closed: 34d65f9d-d1f3-4f9d-a8a4-01d064b6f0e1","timestamp":"2025-06-04 00:06:27:627"}
{"level":"info","message":"AG-UI WebSocket connection established: 2051f1b2-f7ae-4bfb-a9b8-df4de39e3e1e","timestamp":"2025-06-04 00:06:27:627"}
{"level":"info","message":"AG-UI WebSocket connection closed: 2051f1b2-f7ae-4bfb-a9b8-df4de39e3e1e","timestamp":"2025-06-04 00:06:27:627"}
{"level":"info","message":"AG-UI WebSocket connection established: a1d811fc-9e24-4f93-ad2e-e09282d3b97f","timestamp":"2025-06-04 00:06:27:627"}
{"level":"info","message":"AG-UI WebSocket connection established: 30b5ec3b-e427-474b-b007-8167c6956b9b","timestamp":"2025-06-04 00:06:27:627"}
{"level":"info","message":"AG-UI WebSocket connection established: de7e25d3-31a3-4198-a223-bef50eac5bf2","timestamp":"2025-06-04 00:06:29:629"}
{"contentLength":"123","duration":1550,"ip":"::1","level":"info","message":"API: GET /api/research/health - 200 (1550ms)","method":"GET","statusCode":200,"timestamp":"2025-06-04 00:06:51:651","url":"/api/research/health","userAgent":"curl/8.5.0"}
{"level":"info","message":"📴 Received SIGTERM. Starting graceful shutdown...","timestamp":"2025-06-04 00:07:32:732"}
{"level":"info","message":"📴 Received SIGTERM. Starting graceful shutdown...","timestamp":"2025-06-04 00:07:32:732"}
{"level":"info","message":"Initializing database connections...","timestamp":"2025-06-04 00:07:34:734"}
{"level":"info","message":"Connecting to Neo4j database...","timestamp":"2025-06-04 00:07:34:734"}
{"level":"info","message":"✅ Neo4j connection established successfully","timestamp":"2025-06-04 00:07:34:734"}
{"level":"info","message":"Initializing database connections...","timestamp":"2025-06-04 00:07:34:734"}
{"level":"info","message":"Connecting to Neo4j database...","timestamp":"2025-06-04 00:07:34:734"}
{"level":"info","message":"Initializing Neo4j schema...","timestamp":"2025-06-04 00:07:34:734"}
{"level":"info","message":"✅ Neo4j connection established successfully","timestamp":"2025-06-04 00:07:34:734"}
{"level":"info","message":"Initializing Neo4j schema...","timestamp":"2025-06-04 00:07:34:734"}
{"level":"info","message":"✅ Neo4j schema initialized successfully","timestamp":"2025-06-04 00:07:34:734"}
{"level":"info","message":"Initializing database connections...","timestamp":"2025-06-04 00:07:34:734"}
{"level":"info","message":"Connecting to Neo4j database...","timestamp":"2025-06-04 00:07:34:734"}
{"level":"info","message":"✅ Neo4j schema initialized successfully","timestamp":"2025-06-04 00:07:34:734"}
{"level":"info","message":"✅ Neo4j connected successfully","timestamp":"2025-06-04 00:07:34:734"}
{"level":"info","message":"Connecting to Weaviate vector database...","timestamp":"2025-06-04 00:07:34:734"}
{"level":"info","message":"✅ Weaviate connection established successfully","timestamp":"2025-06-04 00:07:34:734"}
{"level":"info","message":"✅ Neo4j connected successfully","timestamp":"2025-06-04 00:07:34:734"}
{"level":"info","message":"Connecting to Weaviate vector database...","timestamp":"2025-06-04 00:07:34:734"}
{"level":"info","message":"✅ Neo4j connection established successfully","timestamp":"2025-06-04 00:07:34:734"}
{"level":"info","message":"✅ Weaviate connection established successfully","timestamp":"2025-06-04 00:07:34:734"}
{"level":"info","message":"Initializing Neo4j schema...","timestamp":"2025-06-04 00:07:34:734"}
{"level":"info","message":"Initializing Weaviate schemas...","timestamp":"2025-06-04 00:07:34:734"}
{"error":"usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","level":"error","message":"Failed to create schema: Supplement","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 00:07:34:734"}
{"error":"usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","level":"error","message":"❌ Failed to initialize Weaviate schemas","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 00:07:34:734"}
{"level":"warn","message":"⚠️ Failed to connect to Weaviate, continuing without vector database: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 00:07:34:734"}
{"level":"info","message":"✅ Weaviate connected successfully","timestamp":"2025-06-04 00:07:34:734"}
{"level":"info","message":"Connecting to Redis cache...","timestamp":"2025-06-04 00:07:34:734"}
{"level":"info","message":"Redis client connected","timestamp":"2025-06-04 00:07:34:734"}
{"level":"info","message":"Initializing Weaviate schemas...","timestamp":"2025-06-04 00:07:34:734"}
{"error":"usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","level":"error","message":"Failed to create schema: Supplement","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 00:07:34:734"}
{"error":"usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","level":"error","message":"❌ Failed to initialize Weaviate schemas","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 00:07:34:734"}
{"level":"warn","message":"⚠️ Failed to connect to Weaviate, continuing without vector database: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 00:07:34:734"}
{"level":"info","message":"✅ Weaviate connected successfully","timestamp":"2025-06-04 00:07:34:734"}
{"level":"info","message":"Connecting to Redis cache...","timestamp":"2025-06-04 00:07:34:734"}
{"level":"info","message":"Redis client connected","timestamp":"2025-06-04 00:07:34:734"}
{"level":"info","message":"Redis client ready","timestamp":"2025-06-04 00:07:34:734"}
{"level":"info","message":"Redis client ready","timestamp":"2025-06-04 00:07:34:734"}
{"level":"info","message":"✅ Redis connection established successfully","timestamp":"2025-06-04 00:07:34:734"}
{"level":"info","message":"✅ Redis connected successfully","timestamp":"2025-06-04 00:07:34:734"}
{"level":"info","message":"Connecting to MongoDB...","timestamp":"2025-06-04 00:07:34:734"}
{"level":"info","message":"✅ Redis connection established successfully","timestamp":"2025-06-04 00:07:34:734"}
{"level":"info","message":"✅ Redis connected successfully","timestamp":"2025-06-04 00:07:34:734"}
{"level":"info","message":"Connecting to MongoDB...","timestamp":"2025-06-04 00:07:34:734"}
{"level":"info","message":"✅ MongoDB connection established successfully","timestamp":"2025-06-04 00:07:34:734"}
{"level":"info","message":"✅ MongoDB connection established successfully","timestamp":"2025-06-04 00:07:34:734"}
{"level":"info","message":"✅ Neo4j schema initialized successfully","timestamp":"2025-06-04 00:07:34:734"}
{"level":"info","message":"✅ MongoDB connected successfully","timestamp":"2025-06-04 00:07:34:734"}
{"level":"info","message":"🚀 All databases connected successfully","timestamp":"2025-06-04 00:07:34:734"}
{"level":"info","message":"🚀 Server running on port 3000","timestamp":"2025-06-04 00:07:34:734"}
{"level":"info","message":"📊 Environment: development","timestamp":"2025-06-04 00:07:34:734"}
{"level":"info","message":"🔗 API URL: http://localhost:3000/api","timestamp":"2025-06-04 00:07:34:734"}
{"level":"info","message":"🔌 AG-UI WebSocket: ws://localhost:3000/agui/ws","timestamp":"2025-06-04 00:07:34:734"}
{"level":"info","message":"💚 Health check: http://localhost:3000/health","timestamp":"2025-06-04 00:07:34:734"}
{"level":"info","message":"✅ Neo4j connected successfully","timestamp":"2025-06-04 00:07:34:734"}
{"level":"info","message":"Connecting to Weaviate vector database...","timestamp":"2025-06-04 00:07:34:734"}
{"level":"info","message":"✅ Weaviate connection established successfully","timestamp":"2025-06-04 00:07:34:734"}
{"level":"info","message":"Initializing Weaviate schemas...","timestamp":"2025-06-04 00:07:34:734"}
{"error":"usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","level":"error","message":"Failed to create schema: Supplement","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 00:07:34:734"}
{"error":"usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","level":"error","message":"❌ Failed to initialize Weaviate schemas","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 00:07:34:734"}
{"level":"warn","message":"⚠️ Failed to connect to Weaviate, continuing without vector database: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 00:07:34:734"}
{"level":"info","message":"✅ Weaviate connected successfully","timestamp":"2025-06-04 00:07:34:734"}
{"level":"info","message":"Connecting to Redis cache...","timestamp":"2025-06-04 00:07:34:734"}
{"level":"info","message":"Redis client connected","timestamp":"2025-06-04 00:07:34:734"}
{"level":"info","message":"Redis client ready","timestamp":"2025-06-04 00:07:34:734"}
{"level":"info","message":"✅ Redis connection established successfully","timestamp":"2025-06-04 00:07:34:734"}
{"level":"info","message":"✅ Redis connected successfully","timestamp":"2025-06-04 00:07:34:734"}
{"level":"info","message":"Connecting to MongoDB...","timestamp":"2025-06-04 00:07:34:734"}
{"level":"info","message":"✅ MongoDB connection established successfully","timestamp":"2025-06-04 00:07:34:734"}
{"level":"info","message":"Initializing database connections...","timestamp":"2025-06-04 00:07:39:739"}
{"level":"info","message":"Connecting to Neo4j database...","timestamp":"2025-06-04 00:07:39:739"}
{"level":"info","message":"✅ Neo4j connection established successfully","timestamp":"2025-06-04 00:07:39:739"}
{"level":"info","message":"Initializing Neo4j schema...","timestamp":"2025-06-04 00:07:39:739"}
{"level":"info","message":"✅ Neo4j schema initialized successfully","timestamp":"2025-06-04 00:07:39:739"}
{"level":"info","message":"✅ Neo4j connected successfully","timestamp":"2025-06-04 00:07:39:739"}
{"level":"info","message":"Connecting to Weaviate vector database...","timestamp":"2025-06-04 00:07:39:739"}
{"level":"info","message":"✅ Weaviate connection established successfully","timestamp":"2025-06-04 00:07:39:739"}
{"level":"info","message":"Initializing Weaviate schemas...","timestamp":"2025-06-04 00:07:39:739"}
{"error":"usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","level":"error","message":"Failed to create schema: Supplement","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 00:07:39:739"}
{"error":"usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","level":"error","message":"❌ Failed to initialize Weaviate schemas","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 00:07:39:739"}
{"level":"warn","message":"⚠️ Failed to connect to Weaviate, continuing without vector database: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 00:07:39:739"}
{"level":"info","message":"✅ Weaviate connected successfully","timestamp":"2025-06-04 00:07:39:739"}
{"level":"info","message":"Connecting to Redis cache...","timestamp":"2025-06-04 00:07:39:739"}
{"level":"info","message":"Redis client connected","timestamp":"2025-06-04 00:07:39:739"}
{"level":"info","message":"Redis client ready","timestamp":"2025-06-04 00:07:39:739"}
{"level":"info","message":"✅ Redis connection established successfully","timestamp":"2025-06-04 00:07:39:739"}
{"level":"info","message":"✅ Redis connected successfully","timestamp":"2025-06-04 00:07:39:739"}
{"level":"info","message":"Connecting to MongoDB...","timestamp":"2025-06-04 00:07:39:739"}
{"level":"info","message":"✅ MongoDB connection established successfully","timestamp":"2025-06-04 00:07:39:739"}
{"level":"info","message":"✅ MongoDB connected successfully","timestamp":"2025-06-04 00:07:39:739"}
{"level":"info","message":"🚀 All databases connected successfully","timestamp":"2025-06-04 00:07:39:739"}
{"level":"info","message":"🚀 Server running on port 3001","timestamp":"2025-06-04 00:07:39:739"}
{"level":"info","message":"📊 Environment: development","timestamp":"2025-06-04 00:07:39:739"}
{"level":"info","message":"🔗 API URL: http://localhost:3001/api","timestamp":"2025-06-04 00:07:39:739"}
{"level":"info","message":"🔌 AG-UI WebSocket: ws://localhost:3001/agui/ws","timestamp":"2025-06-04 00:07:39:739"}
{"level":"info","message":"💚 Health check: http://localhost:3001/health","timestamp":"2025-06-04 00:07:39:739"}
{"level":"info","message":"AG-UI WebSocket connection established: 86e5ebae-0418-404e-8874-8ff47aaf01d7","timestamp":"2025-06-04 00:07:40:740"}
{"level":"info","message":"AG-UI WebSocket connection established: b1506864-59c2-4885-bce1-a1e66c0b4e05","timestamp":"2025-06-04 00:07:40:740"}
{"level":"info","message":"AG-UI WebSocket connection established: ba410cf2-326c-4039-a560-249938fbece8","timestamp":"2025-06-04 00:07:40:740"}
{"level":"info","message":"AG-UI WebSocket connection established: 708a1167-0e5b-4415-a50b-9d608ba7f807","timestamp":"2025-06-04 00:07:40:740"}
{"level":"info","message":"AG-UI WebSocket connection established: d2a39886-c4d1-4dc2-ac25-44c97566d430","timestamp":"2025-06-04 00:07:40:740"}
{"level":"info","message":"📴 Received SIGTERM. Starting graceful shutdown...","timestamp":"2025-06-04 00:07:45:745"}
{"level":"info","message":"📴 Received SIGTERM. Starting graceful shutdown...","timestamp":"2025-06-04 00:07:45:745"}
{"level":"info","message":"Initializing database connections...","timestamp":"2025-06-04 00:07:47:747"}
{"level":"info","message":"Connecting to Neo4j database...","timestamp":"2025-06-04 00:07:47:747"}
{"level":"info","message":"✅ Neo4j connection established successfully","timestamp":"2025-06-04 00:07:47:747"}
{"level":"info","message":"Initializing Neo4j schema...","timestamp":"2025-06-04 00:07:47:747"}
{"level":"info","message":"Initializing database connections...","timestamp":"2025-06-04 00:07:47:747"}
{"level":"info","message":"Connecting to Neo4j database...","timestamp":"2025-06-04 00:07:47:747"}
{"level":"info","message":"✅ Neo4j connection established successfully","timestamp":"2025-06-04 00:07:47:747"}
{"level":"info","message":"Initializing Neo4j schema...","timestamp":"2025-06-04 00:07:47:747"}
{"level":"info","message":"✅ Neo4j schema initialized successfully","timestamp":"2025-06-04 00:07:47:747"}
{"level":"info","message":"✅ Neo4j connected successfully","timestamp":"2025-06-04 00:07:47:747"}
{"level":"info","message":"Connecting to Weaviate vector database...","timestamp":"2025-06-04 00:07:47:747"}
{"level":"info","message":"Initializing database connections...","timestamp":"2025-06-04 00:07:47:747"}
{"level":"info","message":"Connecting to Neo4j database...","timestamp":"2025-06-04 00:07:47:747"}
{"level":"info","message":"✅ Weaviate connection established successfully","timestamp":"2025-06-04 00:07:47:747"}
{"level":"info","message":"✅ Neo4j connection established successfully","timestamp":"2025-06-04 00:07:47:747"}
{"level":"info","message":"Initializing Weaviate schemas...","timestamp":"2025-06-04 00:07:47:747"}
{"error":"usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","level":"error","message":"Failed to create schema: Supplement","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 00:07:47:747"}
{"error":"usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","level":"error","message":"❌ Failed to initialize Weaviate schemas","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 00:07:47:747"}
{"level":"warn","message":"⚠️ Failed to connect to Weaviate, continuing without vector database: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 00:07:47:747"}
{"level":"info","message":"✅ Weaviate connected successfully","timestamp":"2025-06-04 00:07:47:747"}
{"level":"info","message":"Connecting to Redis cache...","timestamp":"2025-06-04 00:07:47:747"}
{"level":"info","message":"Initializing Neo4j schema...","timestamp":"2025-06-04 00:07:47:747"}
{"level":"info","message":"✅ Neo4j schema initialized successfully","timestamp":"2025-06-04 00:07:47:747"}
{"level":"info","message":"Redis client connected","timestamp":"2025-06-04 00:07:47:747"}
{"level":"info","message":"Redis client ready","timestamp":"2025-06-04 00:07:47:747"}
{"level":"info","message":"✅ Neo4j connected successfully","timestamp":"2025-06-04 00:07:47:747"}
{"level":"info","message":"Connecting to Weaviate vector database...","timestamp":"2025-06-04 00:07:47:747"}
{"level":"info","message":"✅ Weaviate connection established successfully","timestamp":"2025-06-04 00:07:47:747"}
{"level":"info","message":"✅ Redis connection established successfully","timestamp":"2025-06-04 00:07:47:747"}
{"level":"info","message":"✅ Redis connected successfully","timestamp":"2025-06-04 00:07:47:747"}
{"level":"info","message":"Connecting to MongoDB...","timestamp":"2025-06-04 00:07:47:747"}
{"level":"info","message":"Initializing Weaviate schemas...","timestamp":"2025-06-04 00:07:47:747"}
{"error":"usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","level":"error","message":"Failed to create schema: Supplement","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 00:07:47:747"}
{"error":"usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","level":"error","message":"❌ Failed to initialize Weaviate schemas","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 00:07:47:747"}
{"level":"warn","message":"⚠️ Failed to connect to Weaviate, continuing without vector database: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 00:07:47:747"}
{"level":"info","message":"✅ Weaviate connected successfully","timestamp":"2025-06-04 00:07:47:747"}
{"level":"info","message":"Connecting to Redis cache...","timestamp":"2025-06-04 00:07:47:747"}
{"level":"info","message":"Redis client connected","timestamp":"2025-06-04 00:07:47:747"}
{"level":"info","message":"Redis client ready","timestamp":"2025-06-04 00:07:47:747"}
{"level":"info","message":"✅ MongoDB connection established successfully","timestamp":"2025-06-04 00:07:47:747"}
{"level":"info","message":"✅ MongoDB connected successfully","timestamp":"2025-06-04 00:07:47:747"}
{"level":"info","message":"🚀 All databases connected successfully","timestamp":"2025-06-04 00:07:47:747"}
{"level":"info","message":"🚀 Server running on port 3000","timestamp":"2025-06-04 00:07:47:747"}
{"level":"info","message":"📊 Environment: development","timestamp":"2025-06-04 00:07:47:747"}
{"level":"info","message":"🔗 API URL: http://localhost:3000/api","timestamp":"2025-06-04 00:07:47:747"}
{"level":"info","message":"🔌 AG-UI WebSocket: ws://localhost:3000/agui/ws","timestamp":"2025-06-04 00:07:47:747"}
{"level":"info","message":"💚 Health check: http://localhost:3000/health","timestamp":"2025-06-04 00:07:47:747"}
{"level":"info","message":"✅ Redis connection established successfully","timestamp":"2025-06-04 00:07:47:747"}
{"level":"info","message":"✅ Redis connected successfully","timestamp":"2025-06-04 00:07:47:747"}
{"level":"info","message":"Connecting to MongoDB...","timestamp":"2025-06-04 00:07:47:747"}
{"level":"info","message":"✅ Neo4j schema initialized successfully","timestamp":"2025-06-04 00:07:47:747"}
{"level":"info","message":"✅ Neo4j connected successfully","timestamp":"2025-06-04 00:07:47:747"}
{"level":"info","message":"Connecting to Weaviate vector database...","timestamp":"2025-06-04 00:07:47:747"}
{"level":"info","message":"✅ MongoDB connection established successfully","timestamp":"2025-06-04 00:07:47:747"}
{"level":"info","message":"✅ Weaviate connection established successfully","timestamp":"2025-06-04 00:07:47:747"}
{"level":"info","message":"Initializing Weaviate schemas...","timestamp":"2025-06-04 00:07:47:747"}
{"error":"usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","level":"error","message":"Failed to create schema: Supplement","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 00:07:47:747"}
{"error":"usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","level":"error","message":"❌ Failed to initialize Weaviate schemas","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 00:07:47:747"}
{"level":"warn","message":"⚠️ Failed to connect to Weaviate, continuing without vector database: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 00:07:47:747"}
{"level":"info","message":"✅ Weaviate connected successfully","timestamp":"2025-06-04 00:07:47:747"}
{"level":"info","message":"Connecting to Redis cache...","timestamp":"2025-06-04 00:07:47:747"}
{"level":"info","message":"Redis client connected","timestamp":"2025-06-04 00:07:47:747"}
{"level":"info","message":"Redis client ready","timestamp":"2025-06-04 00:07:47:747"}
{"level":"info","message":"✅ Redis connection established successfully","timestamp":"2025-06-04 00:07:47:747"}
{"level":"info","message":"✅ Redis connected successfully","timestamp":"2025-06-04 00:07:47:747"}
{"level":"info","message":"Connecting to MongoDB...","timestamp":"2025-06-04 00:07:47:747"}
{"level":"info","message":"✅ MongoDB connection established successfully","timestamp":"2025-06-04 00:07:47:747"}
{"level":"info","message":"Initializing database connections...","timestamp":"2025-06-04 00:07:52:752"}
{"level":"info","message":"Connecting to Neo4j database...","timestamp":"2025-06-04 00:07:52:752"}
{"level":"info","message":"✅ Neo4j connection established successfully","timestamp":"2025-06-04 00:07:52:752"}
{"level":"info","message":"Initializing Neo4j schema...","timestamp":"2025-06-04 00:07:52:752"}
{"level":"info","message":"✅ Neo4j schema initialized successfully","timestamp":"2025-06-04 00:07:52:752"}
{"level":"info","message":"✅ Neo4j connected successfully","timestamp":"2025-06-04 00:07:52:752"}
{"level":"info","message":"Connecting to Weaviate vector database...","timestamp":"2025-06-04 00:07:52:752"}
{"level":"info","message":"✅ Weaviate connection established successfully","timestamp":"2025-06-04 00:07:52:752"}
{"level":"info","message":"Initializing Weaviate schemas...","timestamp":"2025-06-04 00:07:52:752"}
{"error":"usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","level":"error","message":"Failed to create schema: Supplement","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 00:07:52:752"}
{"error":"usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","level":"error","message":"❌ Failed to initialize Weaviate schemas","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 00:07:52:752"}
{"level":"warn","message":"⚠️ Failed to connect to Weaviate, continuing without vector database: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 00:07:52:752"}
{"level":"info","message":"✅ Weaviate connected successfully","timestamp":"2025-06-04 00:07:52:752"}
{"level":"info","message":"Connecting to Redis cache...","timestamp":"2025-06-04 00:07:52:752"}
{"level":"info","message":"Redis client connected","timestamp":"2025-06-04 00:07:52:752"}
{"level":"info","message":"Redis client ready","timestamp":"2025-06-04 00:07:52:752"}
{"level":"info","message":"✅ Redis connection established successfully","timestamp":"2025-06-04 00:07:52:752"}
{"level":"info","message":"✅ Redis connected successfully","timestamp":"2025-06-04 00:07:52:752"}
{"level":"info","message":"Connecting to MongoDB...","timestamp":"2025-06-04 00:07:52:752"}
{"level":"info","message":"✅ MongoDB connection established successfully","timestamp":"2025-06-04 00:07:52:752"}
{"level":"info","message":"✅ MongoDB connected successfully","timestamp":"2025-06-04 00:07:52:752"}
{"level":"info","message":"🚀 All databases connected successfully","timestamp":"2025-06-04 00:07:52:752"}
{"level":"info","message":"🚀 Server running on port 3001","timestamp":"2025-06-04 00:07:52:752"}
{"level":"info","message":"📊 Environment: development","timestamp":"2025-06-04 00:07:52:752"}
{"level":"info","message":"🔗 API URL: http://localhost:3001/api","timestamp":"2025-06-04 00:07:52:752"}
{"level":"info","message":"🔌 AG-UI WebSocket: ws://localhost:3001/agui/ws","timestamp":"2025-06-04 00:07:52:752"}
{"level":"info","message":"💚 Health check: http://localhost:3001/health","timestamp":"2025-06-04 00:07:52:752"}
{"level":"info","message":"AG-UI WebSocket connection established: cfca790b-696e-41f5-b0a5-1aab133cea9d","timestamp":"2025-06-04 00:07:52:752"}
{"level":"info","message":"AG-UI WebSocket connection established: 3a6285cd-3868-47e2-b367-c1c40003add3","timestamp":"2025-06-04 00:07:52:752"}
{"level":"info","message":"AG-UI WebSocket connection established: 5e746ff7-2348-429d-9c95-a976ff85649a","timestamp":"2025-06-04 00:07:53:753"}
{"level":"info","message":"AG-UI WebSocket connection established: e2e391f9-6d8f-4bd8-ab57-c772a720763d","timestamp":"2025-06-04 00:07:53:753"}
{"level":"info","message":"AG-UI WebSocket connection established: 6722fcaa-265f-4619-bc18-85379f199e3e","timestamp":"2025-06-04 00:07:53:753"}
{"contentLength":"263766","duration":7717,"ip":"::1","level":"info","message":"API: POST /api/research/web-search - 200 (7717ms)","method":"POST","statusCode":200,"timestamp":"2025-06-04 00:08:10:810","url":"/api/research/web-search","userAgent":"curl/8.5.0"}
{"contentLength":"123","duration":1872,"ip":"::1","level":"info","message":"API: GET /api/research/health - 200 (1872ms)","method":"GET","statusCode":200,"timestamp":"2025-06-04 00:08:24:824","url":"/api/research/health","userAgent":"curl/8.5.0"}
{"level":"info","message":"AG-UI WebSocket connection closed: cfca790b-696e-41f5-b0a5-1aab133cea9d","timestamp":"2025-06-04 00:09:22:922"}
{"level":"info","message":"AG-UI WebSocket connection closed: 5e746ff7-2348-429d-9c95-a976ff85649a","timestamp":"2025-06-04 00:09:22:922"}
{"level":"info","message":"AG-UI WebSocket connection established: 2a685efb-93a0-497a-b4b5-582dd05439f6","timestamp":"2025-06-04 00:09:23:923"}
{"level":"info","message":"AG-UI WebSocket connection closed: 2a685efb-93a0-497a-b4b5-582dd05439f6","timestamp":"2025-06-04 00:09:23:923"}
{"level":"info","message":"AG-UI WebSocket connection established: 3f041fa3-5d1a-4adb-8351-448ffd1c913e","timestamp":"2025-06-04 00:09:23:923"}
{"level":"info","message":"AG-UI WebSocket connection established: 5cf66980-52ae-4bdc-8eae-855ca46f43c6","timestamp":"2025-06-04 00:09:24:924"}
{"level":"info","message":"Starting Gemma analysis for supplement: tyrozyna","timestamp":"2025-06-04 00:09:29:929"}
{"level":"warn","message":"Empty or undefined response from Gemma for tyrozyna, using fallback","timestamp":"2025-06-04 00:09:29:929"}
{"level":"info","message":"Gemma analysis completed for tyrozyna","timestamp":"2025-06-04 00:09:29:929"}
{"duration":309,"level":"info","message":"Graph: Search nodes (309ms)","nodeCount":0,"operation":"Search nodes","relationshipCount":0,"timestamp":"2025-06-04 00:09:30:930"}
{"duration":585,"level":"info","message":"Graph: Create node (1 nodes) (585ms)","nodeCount":1,"operation":"Create node","relationshipCount":0,"timestamp":"2025-06-04 00:09:30:930"}
{"duration":268,"level":"info","message":"Graph: Search nodes (268ms)","nodeCount":0,"operation":"Search nodes","relationshipCount":0,"timestamp":"2025-06-04 00:09:30:930"}
{"duration":40,"level":"info","message":"Graph: Create node (1 nodes) (40ms)","nodeCount":1,"operation":"Create node","relationshipCount":0,"timestamp":"2025-06-04 00:09:30:930"}
{"duration":213,"level":"info","message":"Graph: Create relationship (1 relationships) (213ms)","nodeCount":0,"operation":"Create relationship","relationshipCount":1,"timestamp":"2025-06-04 00:09:31:931"}
{"duration":57,"level":"info","message":"Graph: Search nodes (57ms)","nodeCount":0,"operation":"Search nodes","relationshipCount":0,"timestamp":"2025-06-04 00:09:31:931"}
{"duration":38,"level":"info","message":"Graph: Create node (1 nodes) (38ms)","nodeCount":1,"operation":"Create node","relationshipCount":0,"timestamp":"2025-06-04 00:09:31:931"}
{"duration":78,"level":"info","message":"Graph: Create relationship (1 relationships) (78ms)","nodeCount":0,"operation":"Create relationship","relationshipCount":1,"timestamp":"2025-06-04 00:09:31:931"}
{"duration":36,"level":"info","message":"Graph: Create node (1 nodes) (36ms)","nodeCount":1,"operation":"Create node","relationshipCount":0,"timestamp":"2025-06-04 00:09:31:931"}
{"duration":57,"level":"info","message":"Graph: Create relationship (1 relationships) (57ms)","nodeCount":0,"operation":"Create relationship","relationshipCount":1,"timestamp":"2025-06-04 00:09:31:931"}
{"duration":9,"level":"info","message":"Graph: Create node (1 nodes) (9ms)","nodeCount":1,"operation":"Create node","relationshipCount":0,"timestamp":"2025-06-04 00:09:31:931"}
{"duration":11,"level":"info","message":"Graph: Create relationship (1 relationships) (11ms)","nodeCount":0,"operation":"Create relationship","relationshipCount":1,"timestamp":"2025-06-04 00:09:31:931"}
{"duration":8,"level":"info","message":"Graph: Create node (1 nodes) (8ms)","nodeCount":1,"operation":"Create node","relationshipCount":0,"timestamp":"2025-06-04 00:09:31:931"}
{"duration":11,"level":"info","message":"Graph: Create relationship (1 relationships) (11ms)","nodeCount":0,"operation":"Create relationship","relationshipCount":1,"timestamp":"2025-06-04 00:09:31:931"}
{"duration":9,"level":"info","message":"Graph: Create node (1 nodes) (9ms)","nodeCount":1,"operation":"Create node","relationshipCount":0,"timestamp":"2025-06-04 00:09:31:931"}
{"duration":10,"level":"info","message":"Graph: Create relationship (1 relationships) (10ms)","nodeCount":0,"operation":"Create relationship","relationshipCount":1,"timestamp":"2025-06-04 00:09:31:931"}
{"duration":9,"level":"info","message":"Graph: Create node (1 nodes) (9ms)","nodeCount":1,"operation":"Create node","relationshipCount":0,"timestamp":"2025-06-04 00:09:31:931"}
{"duration":11,"level":"info","message":"Graph: Create relationship (1 relationships) (11ms)","nodeCount":0,"operation":"Create relationship","relationshipCount":1,"timestamp":"2025-06-04 00:09:31:931"}
{"duration":1764,"level":"info","message":"Graph: Update supplement knowledge (7 nodes) (7 relationships) (1764ms)","nodeCount":7,"operation":"Update supplement knowledge","relationshipCount":7,"timestamp":"2025-06-04 00:09:31:931"}
{"level":"error","message":"Error parsing insight response: \"undefined\" is not valid JSON","stack":"SyntaxError: \"undefined\" is not valid JSON\n    at JSON.parse (<anonymous>)\n    at GemmaService.parseInsightResponse (/home/<USER>/Suplementor/backend/src/services/GemmaService.ts:294:27)\n    at GemmaService.generateInsights (/home/<USER>/Suplementor/backend/src/services/GemmaService.ts:105:19)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async SupplementResearchAgent.executeResearchPipeline (/home/<USER>/Suplementor/backend/src/agents/SupplementResearchAgent.ts:168:24)","timestamp":"2025-06-04 00:09:31:931"}
{"level":"info","message":"Agent completed for 5cf66980-52ae-4bdc-8eae-855ca46f43c6-be6385e1-ecc7-4d76-98b8-39069fc83541","timestamp":"2025-06-04 00:09:31:931"}
{"level":"info","message":"AG-UI WebSocket connection closed: 5cf66980-52ae-4bdc-8eae-855ca46f43c6","timestamp":"2025-06-04 00:09:31:931"}
{"level":"info","message":"AG-UI WebSocket connection established: 287ca91c-5bc9-4549-acc2-8573542b9a59","timestamp":"2025-06-04 00:11:22:1122"}
{"level":"info","message":"AG-UI WebSocket connection closed: 287ca91c-5bc9-4549-acc2-8573542b9a59","timestamp":"2025-06-04 00:11:22:1122"}
{"level":"info","message":"AG-UI WebSocket connection established: 9201042a-646c-4f3a-a7eb-911399e2945e","timestamp":"2025-06-04 00:11:22:1122"}
{"level":"info","message":"AG-UI WebSocket connection established: 189002f1-c6a5-4c1b-a14b-74caab0c9f45","timestamp":"2025-06-04 00:11:23:1123"}
{"level":"info","message":"Starting Gemma analysis for supplement: tyrozyna","timestamp":"2025-06-04 00:11:34:1134"}
{"level":"warn","message":"Empty or undefined response from Gemma for tyrozyna, using fallback","timestamp":"2025-06-04 00:11:34:1134"}
{"level":"info","message":"Gemma analysis completed for tyrozyna","timestamp":"2025-06-04 00:11:34:1134"}
{"duration":35,"level":"info","message":"Graph: Create node (1 nodes) (35ms)","nodeCount":1,"operation":"Create node","relationshipCount":0,"timestamp":"2025-06-04 00:11:34:1134"}
{"duration":10,"level":"info","message":"Graph: Create node (1 nodes) (10ms)","nodeCount":1,"operation":"Create node","relationshipCount":0,"timestamp":"2025-06-04 00:11:34:1134"}
{"duration":10,"level":"info","message":"Graph: Create relationship (1 relationships) (10ms)","nodeCount":0,"operation":"Create relationship","relationshipCount":1,"timestamp":"2025-06-04 00:11:34:1134"}
{"duration":9,"level":"info","message":"Graph: Create node (1 nodes) (9ms)","nodeCount":1,"operation":"Create node","relationshipCount":0,"timestamp":"2025-06-04 00:11:34:1134"}
{"duration":9,"level":"info","message":"Graph: Create relationship (1 relationships) (9ms)","nodeCount":0,"operation":"Create relationship","relationshipCount":1,"timestamp":"2025-06-04 00:11:34:1134"}
{"duration":11,"level":"info","message":"Graph: Create node (1 nodes) (11ms)","nodeCount":1,"operation":"Create node","relationshipCount":0,"timestamp":"2025-06-04 00:11:34:1134"}
{"duration":8,"level":"info","message":"Graph: Create relationship (1 relationships) (8ms)","nodeCount":0,"operation":"Create relationship","relationshipCount":1,"timestamp":"2025-06-04 00:11:34:1134"}
{"duration":8,"level":"info","message":"Graph: Create node (1 nodes) (8ms)","nodeCount":1,"operation":"Create node","relationshipCount":0,"timestamp":"2025-06-04 00:11:34:1134"}
{"duration":9,"level":"info","message":"Graph: Create relationship (1 relationships) (9ms)","nodeCount":0,"operation":"Create relationship","relationshipCount":1,"timestamp":"2025-06-04 00:11:34:1134"}
{"duration":9,"level":"info","message":"Graph: Create node (1 nodes) (9ms)","nodeCount":1,"operation":"Create node","relationshipCount":0,"timestamp":"2025-06-04 00:11:34:1134"}
{"duration":12,"level":"info","message":"Graph: Create relationship (1 relationships) (12ms)","nodeCount":0,"operation":"Create relationship","relationshipCount":1,"timestamp":"2025-06-04 00:11:34:1134"}
{"duration":8,"level":"info","message":"Graph: Create node (1 nodes) (8ms)","nodeCount":1,"operation":"Create node","relationshipCount":0,"timestamp":"2025-06-04 00:11:34:1134"}
{"duration":8,"level":"info","message":"Graph: Create relationship (1 relationships) (8ms)","nodeCount":0,"operation":"Create relationship","relationshipCount":1,"timestamp":"2025-06-04 00:11:34:1134"}
{"duration":8,"level":"info","message":"Graph: Create node (1 nodes) (8ms)","nodeCount":1,"operation":"Create node","relationshipCount":0,"timestamp":"2025-06-04 00:11:34:1134"}
{"duration":10,"level":"info","message":"Graph: Create relationship (1 relationships) (10ms)","nodeCount":0,"operation":"Create relationship","relationshipCount":1,"timestamp":"2025-06-04 00:11:34:1134"}
{"duration":174,"level":"info","message":"Graph: Update supplement knowledge (7 nodes) (7 relationships) (174ms)","nodeCount":7,"operation":"Update supplement knowledge","relationshipCount":7,"timestamp":"2025-06-04 00:11:34:1134"}
{"level":"error","message":"Error parsing insight response: \"undefined\" is not valid JSON","stack":"SyntaxError: \"undefined\" is not valid JSON\n    at JSON.parse (<anonymous>)\n    at GemmaService.parseInsightResponse (/home/<USER>/Suplementor/backend/src/services/GemmaService.ts:294:27)\n    at GemmaService.generateInsights (/home/<USER>/Suplementor/backend/src/services/GemmaService.ts:105:19)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async SupplementResearchAgent.executeResearchPipeline (/home/<USER>/Suplementor/backend/src/agents/SupplementResearchAgent.ts:168:24)","timestamp":"2025-06-04 00:11:34:1134"}
{"level":"info","message":"Agent completed for 189002f1-c6a5-4c1b-a14b-74caab0c9f45-9872456b-0c7a-4eea-94be-36b2d28fe82c","timestamp":"2025-06-04 00:11:34:1134"}
{"level":"info","message":"AG-UI WebSocket connection closed: 3f041fa3-5d1a-4adb-8351-448ffd1c913e","timestamp":"2025-06-04 00:12:24:1224"}
{"level":"info","message":"AG-UI WebSocket connection closed: 189002f1-c6a5-4c1b-a14b-74caab0c9f45","timestamp":"2025-06-04 00:12:24:1224"}
{"level":"info","message":"AG-UI WebSocket connection closed: 9201042a-646c-4f3a-a7eb-911399e2945e","timestamp":"2025-06-04 00:12:24:1224"}
{"level":"info","message":"AG-UI WebSocket connection established: d6119859-065b-419d-bf77-a9a5436dec21","timestamp":"2025-06-04 00:12:26:1226"}
{"level":"info","message":"AG-UI WebSocket connection closed: d6119859-065b-419d-bf77-a9a5436dec21","timestamp":"2025-06-04 00:12:26:1226"}
{"level":"info","message":"AG-UI WebSocket connection established: 5275195d-7dd1-43d4-aea2-4abbb89835e3","timestamp":"2025-06-04 00:12:26:1226"}
{"level":"info","message":"AG-UI WebSocket connection established: b998e0fd-95c5-4994-a052-419eb180a5ac","timestamp":"2025-06-04 00:12:27:1227"}
{"level":"info","message":"AG-UI WebSocket connection established: e667f732-8e9d-4093-9d60-0db1519c7609","timestamp":"2025-06-04 00:12:37:1237"}
{"level":"info","message":"AG-UI WebSocket connection closed: e667f732-8e9d-4093-9d60-0db1519c7609","timestamp":"2025-06-04 00:12:37:1237"}
{"level":"info","message":"AG-UI WebSocket connection established: 2f36685c-4aef-4ed0-97ee-750f23f5da86","timestamp":"2025-06-04 00:12:37:1237"}
{"level":"info","message":"AG-UI WebSocket connection established: 0bdb4e72-730e-4f42-b434-b13dd3344a1f","timestamp":"2025-06-04 00:12:38:1238"}
{"level":"info","message":"AG-UI WebSocket connection established: 7a850ccd-578d-460b-a485-4b8cbab50f5d","timestamp":"2025-06-04 00:13:03:133"}
{"level":"info","message":"AG-UI WebSocket connection closed: 7a850ccd-578d-460b-a485-4b8cbab50f5d","timestamp":"2025-06-04 00:13:03:133"}
{"level":"info","message":"AG-UI WebSocket connection established: 506c99d3-c5a0-4a0b-ac64-30b593429b96","timestamp":"2025-06-04 00:13:03:133"}
{"level":"info","message":"AG-UI WebSocket connection established: 3997b81d-c838-4ac5-a029-a10af7bc56cf","timestamp":"2025-06-04 00:13:04:134"}
{"level":"info","message":"Starting Gemma analysis for supplement: tyrozyna","timestamp":"2025-06-04 00:13:22:1322"}
{"level":"warn","message":"Empty or undefined response from Gemma for tyrozyna, using fallback","timestamp":"2025-06-04 00:13:22:1322"}
{"level":"info","message":"Gemma analysis completed for tyrozyna","timestamp":"2025-06-04 00:13:22:1322"}
{"duration":19,"level":"info","message":"Graph: Create node (1 nodes) (19ms)","nodeCount":1,"operation":"Create node","relationshipCount":0,"timestamp":"2025-06-04 00:13:22:1322"}
{"duration":6,"level":"info","message":"Graph: Create node (1 nodes) (6ms)","nodeCount":1,"operation":"Create node","relationshipCount":0,"timestamp":"2025-06-04 00:13:22:1322"}
{"duration":8,"level":"info","message":"Graph: Create relationship (1 relationships) (8ms)","nodeCount":0,"operation":"Create relationship","relationshipCount":1,"timestamp":"2025-06-04 00:13:22:1322"}
{"duration":7,"level":"info","message":"Graph: Create node (1 nodes) (7ms)","nodeCount":1,"operation":"Create node","relationshipCount":0,"timestamp":"2025-06-04 00:13:22:1322"}
{"duration":7,"level":"info","message":"Graph: Create relationship (1 relationships) (7ms)","nodeCount":0,"operation":"Create relationship","relationshipCount":1,"timestamp":"2025-06-04 00:13:22:1322"}
{"duration":12,"level":"info","message":"Graph: Create node (1 nodes) (12ms)","nodeCount":1,"operation":"Create node","relationshipCount":0,"timestamp":"2025-06-04 00:13:22:1322"}
{"duration":13,"level":"info","message":"Graph: Create relationship (1 relationships) (13ms)","nodeCount":0,"operation":"Create relationship","relationshipCount":1,"timestamp":"2025-06-04 00:13:22:1322"}
{"duration":8,"level":"info","message":"Graph: Create node (1 nodes) (8ms)","nodeCount":1,"operation":"Create node","relationshipCount":0,"timestamp":"2025-06-04 00:13:22:1322"}
{"duration":8,"level":"info","message":"Graph: Create relationship (1 relationships) (8ms)","nodeCount":0,"operation":"Create relationship","relationshipCount":1,"timestamp":"2025-06-04 00:13:22:1322"}
{"duration":7,"level":"info","message":"Graph: Create node (1 nodes) (7ms)","nodeCount":1,"operation":"Create node","relationshipCount":0,"timestamp":"2025-06-04 00:13:22:1322"}
{"duration":10,"level":"info","message":"Graph: Create relationship (1 relationships) (10ms)","nodeCount":0,"operation":"Create relationship","relationshipCount":1,"timestamp":"2025-06-04 00:13:22:1322"}
{"duration":8,"level":"info","message":"Graph: Create node (1 nodes) (8ms)","nodeCount":1,"operation":"Create node","relationshipCount":0,"timestamp":"2025-06-04 00:13:22:1322"}
{"duration":8,"level":"info","message":"Graph: Create relationship (1 relationships) (8ms)","nodeCount":0,"operation":"Create relationship","relationshipCount":1,"timestamp":"2025-06-04 00:13:22:1322"}
{"duration":7,"level":"info","message":"Graph: Create node (1 nodes) (7ms)","nodeCount":1,"operation":"Create node","relationshipCount":0,"timestamp":"2025-06-04 00:13:22:1322"}
{"duration":8,"level":"info","message":"Graph: Create relationship (1 relationships) (8ms)","nodeCount":0,"operation":"Create relationship","relationshipCount":1,"timestamp":"2025-06-04 00:13:22:1322"}
{"duration":143,"level":"info","message":"Graph: Update supplement knowledge (7 nodes) (7 relationships) (143ms)","nodeCount":7,"operation":"Update supplement knowledge","relationshipCount":7,"timestamp":"2025-06-04 00:13:22:1322"}
{"level":"error","message":"Error parsing insight response: \"undefined\" is not valid JSON","stack":"SyntaxError: \"undefined\" is not valid JSON\n    at JSON.parse (<anonymous>)\n    at GemmaService.parseInsightResponse (/home/<USER>/Suplementor/backend/src/services/GemmaService.ts:294:27)\n    at GemmaService.generateInsights (/home/<USER>/Suplementor/backend/src/services/GemmaService.ts:105:19)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async SupplementResearchAgent.executeResearchPipeline (/home/<USER>/Suplementor/backend/src/agents/SupplementResearchAgent.ts:168:24)","timestamp":"2025-06-04 00:13:22:1322"}
{"level":"info","message":"Agent completed for 3997b81d-c838-4ac5-a029-a10af7bc56cf-56d5a175-1d53-4eb2-bddf-67a00f5bb0f1","timestamp":"2025-06-04 00:13:22:1322"}
{"level":"info","message":"Initializing database connections...","timestamp":"2025-06-04 00:18:35:1835"}
{"level":"info","message":"Connecting to Neo4j database...","timestamp":"2025-06-04 00:18:35:1835"}
{"level":"info","message":"✅ Neo4j connection established successfully","timestamp":"2025-06-04 00:18:35:1835"}
{"level":"info","message":"Initializing Neo4j schema...","timestamp":"2025-06-04 00:18:35:1835"}
{"level":"info","message":"✅ Neo4j schema initialized successfully","timestamp":"2025-06-04 00:18:35:1835"}
{"level":"info","message":"✅ Neo4j connected successfully","timestamp":"2025-06-04 00:18:35:1835"}
{"level":"info","message":"Connecting to Weaviate vector database...","timestamp":"2025-06-04 00:18:35:1835"}
{"level":"info","message":"✅ Weaviate connection established successfully","timestamp":"2025-06-04 00:18:35:1835"}
{"level":"info","message":"Initializing Weaviate schemas...","timestamp":"2025-06-04 00:18:35:1835"}
{"error":"usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","level":"error","message":"Failed to create schema: Supplement","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 00:18:35:1835"}
{"error":"usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","level":"error","message":"❌ Failed to initialize Weaviate schemas","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 00:18:35:1835"}
{"level":"warn","message":"⚠️ Failed to connect to Weaviate, continuing without vector database: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 00:18:35:1835"}
{"level":"info","message":"✅ Weaviate connected successfully","timestamp":"2025-06-04 00:18:35:1835"}
{"level":"info","message":"Connecting to Redis cache...","timestamp":"2025-06-04 00:18:35:1835"}
{"level":"info","message":"Redis client connected","timestamp":"2025-06-04 00:18:35:1835"}
{"level":"info","message":"Redis client ready","timestamp":"2025-06-04 00:18:35:1835"}
{"level":"info","message":"✅ Redis connection established successfully","timestamp":"2025-06-04 00:18:35:1835"}
{"level":"info","message":"✅ Redis connected successfully","timestamp":"2025-06-04 00:18:35:1835"}
{"level":"info","message":"Connecting to MongoDB...","timestamp":"2025-06-04 00:18:35:1835"}
{"level":"info","message":"✅ MongoDB connection established successfully","timestamp":"2025-06-04 00:18:35:1835"}
{"level":"info","message":"✅ MongoDB connected successfully","timestamp":"2025-06-04 00:18:35:1835"}
{"level":"info","message":"🚀 All databases connected successfully","timestamp":"2025-06-04 00:18:35:1835"}
{"level":"info","message":"🚀 Server running on port 3000","timestamp":"2025-06-04 00:18:35:1835"}
{"level":"info","message":"📊 Environment: development","timestamp":"2025-06-04 00:18:35:1835"}
{"level":"info","message":"🔗 API URL: http://localhost:3000/api","timestamp":"2025-06-04 00:18:35:1835"}
{"level":"info","message":"🔌 AG-UI WebSocket: ws://localhost:3000/agui/ws","timestamp":"2025-06-04 00:18:35:1835"}
{"level":"info","message":"💚 Health check: http://localhost:3000/health","timestamp":"2025-06-04 00:18:35:1835"}
{"level":"info","message":"Initializing database connections...","timestamp":"2025-06-04 00:19:33:1933"}
{"level":"info","message":"Connecting to Neo4j database...","timestamp":"2025-06-04 00:19:33:1933"}
{"level":"info","message":"✅ Neo4j connection established successfully","timestamp":"2025-06-04 00:19:33:1933"}
{"level":"info","message":"Initializing Neo4j schema...","timestamp":"2025-06-04 00:19:33:1933"}
{"level":"info","message":"✅ Neo4j schema initialized successfully","timestamp":"2025-06-04 00:19:33:1933"}
{"level":"info","message":"✅ Neo4j connected successfully","timestamp":"2025-06-04 00:19:33:1933"}
{"level":"info","message":"Connecting to Weaviate vector database...","timestamp":"2025-06-04 00:19:33:1933"}
{"level":"info","message":"✅ Weaviate connection established successfully","timestamp":"2025-06-04 00:19:33:1933"}
{"level":"info","message":"Initializing Weaviate schemas...","timestamp":"2025-06-04 00:19:33:1933"}
{"error":"usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","level":"error","message":"Failed to create schema: Supplement","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 00:19:33:1933"}
{"error":"usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","level":"error","message":"❌ Failed to initialize Weaviate schemas","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 00:19:33:1933"}
{"level":"warn","message":"⚠️ Failed to connect to Weaviate, continuing without vector database: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 00:19:33:1933"}
{"level":"info","message":"✅ Weaviate connected successfully","timestamp":"2025-06-04 00:19:33:1933"}
{"level":"info","message":"Connecting to Redis cache...","timestamp":"2025-06-04 00:19:33:1933"}
{"level":"info","message":"Redis client connected","timestamp":"2025-06-04 00:19:33:1933"}
{"level":"info","message":"Redis client ready","timestamp":"2025-06-04 00:19:33:1933"}
{"level":"info","message":"✅ Redis connection established successfully","timestamp":"2025-06-04 00:19:33:1933"}
{"level":"info","message":"✅ Redis connected successfully","timestamp":"2025-06-04 00:19:33:1933"}
{"level":"info","message":"Connecting to MongoDB...","timestamp":"2025-06-04 00:19:33:1933"}
{"level":"info","message":"✅ MongoDB connection established successfully","timestamp":"2025-06-04 00:19:33:1933"}
{"level":"info","message":"✅ MongoDB connected successfully","timestamp":"2025-06-04 00:19:33:1933"}
{"level":"info","message":"🚀 All databases connected successfully","timestamp":"2025-06-04 00:19:33:1933"}
{"level":"info","message":"🚀 Server running on port 3000","timestamp":"2025-06-04 00:19:33:1933"}
{"level":"info","message":"📊 Environment: development","timestamp":"2025-06-04 00:19:33:1933"}
{"level":"info","message":"🔗 API URL: http://localhost:3000/api","timestamp":"2025-06-04 00:19:33:1933"}
{"level":"info","message":"🔌 AG-UI WebSocket: ws://localhost:3000/agui/ws","timestamp":"2025-06-04 00:19:33:1933"}
{"level":"info","message":"💚 Health check: http://localhost:3000/health","timestamp":"2025-06-04 00:19:33:1933"}
{"level":"info","message":"Initializing database connections...","timestamp":"2025-06-04 00:24:18:2418"}
{"level":"info","message":"Connecting to Neo4j database...","timestamp":"2025-06-04 00:24:18:2418"}
{"level":"info","message":"✅ Neo4j connection established successfully","timestamp":"2025-06-04 00:24:18:2418"}
{"level":"info","message":"Initializing Neo4j schema...","timestamp":"2025-06-04 00:24:18:2418"}
{"level":"info","message":"✅ Neo4j schema initialized successfully","timestamp":"2025-06-04 00:24:18:2418"}
{"level":"info","message":"✅ Neo4j connected successfully","timestamp":"2025-06-04 00:24:18:2418"}
{"level":"info","message":"Connecting to Weaviate vector database...","timestamp":"2025-06-04 00:24:18:2418"}
{"level":"info","message":"✅ Weaviate connection established successfully","timestamp":"2025-06-04 00:24:18:2418"}
{"level":"info","message":"Initializing Weaviate schemas...","timestamp":"2025-06-04 00:24:18:2418"}
{"error":"usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","level":"error","message":"Failed to create schema: Supplement","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 00:24:18:2418"}
{"error":"usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","level":"error","message":"❌ Failed to initialize Weaviate schemas","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 00:24:18:2418"}
{"level":"warn","message":"⚠️ Failed to connect to Weaviate, continuing without vector database: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 00:24:18:2418"}
{"level":"info","message":"✅ Weaviate connected successfully","timestamp":"2025-06-04 00:24:18:2418"}
{"level":"info","message":"Connecting to Redis cache...","timestamp":"2025-06-04 00:24:18:2418"}
{"level":"info","message":"Redis client connected","timestamp":"2025-06-04 00:24:18:2418"}
{"level":"info","message":"Redis client ready","timestamp":"2025-06-04 00:24:18:2418"}
{"level":"info","message":"✅ Redis connection established successfully","timestamp":"2025-06-04 00:24:18:2418"}
{"level":"info","message":"✅ Redis connected successfully","timestamp":"2025-06-04 00:24:18:2418"}
{"level":"info","message":"Connecting to MongoDB...","timestamp":"2025-06-04 00:24:18:2418"}
{"level":"info","message":"✅ MongoDB connection established successfully","timestamp":"2025-06-04 00:24:18:2418"}
{"level":"info","message":"✅ MongoDB connected successfully","timestamp":"2025-06-04 00:24:18:2418"}
{"level":"info","message":"🚀 All databases connected successfully","timestamp":"2025-06-04 00:24:18:2418"}
{"level":"info","message":"🚀 Server running on port 3000","timestamp":"2025-06-04 00:24:18:2418"}
{"level":"info","message":"📊 Environment: development","timestamp":"2025-06-04 00:24:18:2418"}
{"level":"info","message":"🔗 API URL: http://localhost:3000/api","timestamp":"2025-06-04 00:24:18:2418"}
{"level":"info","message":"🔌 AG-UI WebSocket: ws://localhost:3000/agui/ws","timestamp":"2025-06-04 00:24:18:2418"}
{"level":"info","message":"💚 Health check: http://localhost:3000/health","timestamp":"2025-06-04 00:24:18:2418"}
{"level":"info","message":"Initializing database connections...","timestamp":"2025-06-04 00:25:05:255"}
{"level":"info","message":"Connecting to Neo4j database...","timestamp":"2025-06-04 00:25:05:255"}
{"level":"info","message":"✅ Neo4j connection established successfully","timestamp":"2025-06-04 00:25:05:255"}
{"level":"info","message":"Initializing Neo4j schema...","timestamp":"2025-06-04 00:25:05:255"}
{"level":"info","message":"✅ Neo4j schema initialized successfully","timestamp":"2025-06-04 00:25:05:255"}
{"level":"info","message":"✅ Neo4j connected successfully","timestamp":"2025-06-04 00:25:05:255"}
{"level":"info","message":"Connecting to Weaviate vector database...","timestamp":"2025-06-04 00:25:05:255"}
{"level":"info","message":"✅ Weaviate connection established successfully","timestamp":"2025-06-04 00:25:05:255"}
{"level":"info","message":"Initializing Weaviate schemas...","timestamp":"2025-06-04 00:25:05:255"}
{"error":"usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","level":"error","message":"Failed to create schema: Supplement","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 00:25:05:255"}
{"error":"usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","level":"error","message":"❌ Failed to initialize Weaviate schemas","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 00:25:05:255"}
{"level":"warn","message":"⚠️ Failed to connect to Weaviate, continuing without vector database: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-04 00:25:05:255"}
{"level":"info","message":"✅ Weaviate connected successfully","timestamp":"2025-06-04 00:25:05:255"}
{"level":"info","message":"Connecting to Redis cache...","timestamp":"2025-06-04 00:25:05:255"}
{"level":"info","message":"Redis client connected","timestamp":"2025-06-04 00:25:05:255"}
{"level":"info","message":"Redis client ready","timestamp":"2025-06-04 00:25:05:255"}
{"level":"info","message":"✅ Redis connection established successfully","timestamp":"2025-06-04 00:25:05:255"}
{"level":"info","message":"✅ Redis connected successfully","timestamp":"2025-06-04 00:25:05:255"}
{"level":"info","message":"Connecting to MongoDB...","timestamp":"2025-06-04 00:25:05:255"}
{"level":"info","message":"✅ MongoDB connection established successfully","timestamp":"2025-06-04 00:25:05:255"}
{"level":"info","message":"✅ MongoDB connected successfully","timestamp":"2025-06-04 00:25:05:255"}
{"level":"info","message":"🚀 All databases connected successfully","timestamp":"2025-06-04 00:25:05:255"}
{"level":"info","message":"🚀 Server running on port 3000","timestamp":"2025-06-04 00:25:05:255"}
{"level":"info","message":"📊 Environment: development","timestamp":"2025-06-04 00:25:05:255"}
{"level":"info","message":"🔗 API URL: http://localhost:3000/api","timestamp":"2025-06-04 00:25:05:255"}
{"level":"info","message":"🔌 AG-UI WebSocket: ws://localhost:3000/agui/ws","timestamp":"2025-06-04 00:25:05:255"}
{"level":"info","message":"💚 Health check: http://localhost:3000/health","timestamp":"2025-06-04 00:25:05:255"}
{"level":"info","message":"AG-UI WebSocket connection established: a7030ca1-6b15-4427-9330-ef7eb021c14e","timestamp":"2025-06-04 00:26:00:260"}
{"level":"info","message":"Starting Gemma analysis for supplement: tyrozyna","timestamp":"2025-06-04 00:26:09:269"}
{"level":"warn","message":"Empty or undefined response from Gemma for tyrozyna, using fallback","timestamp":"2025-06-04 00:26:09:269"}
{"level":"info","message":"Gemma analysis completed for tyrozyna","timestamp":"2025-06-04 00:26:09:269"}
{"duration":26,"level":"info","message":"Graph: Create node (1 nodes) (26ms)","nodeCount":1,"operation":"Create node","relationshipCount":0,"timestamp":"2025-06-04 00:26:09:269"}
{"duration":17,"level":"info","message":"Graph: Create node (1 nodes) (17ms)","nodeCount":1,"operation":"Create node","relationshipCount":0,"timestamp":"2025-06-04 00:26:09:269"}
{"duration":44,"level":"info","message":"Graph: Create relationship (1 relationships) (44ms)","nodeCount":0,"operation":"Create relationship","relationshipCount":1,"timestamp":"2025-06-04 00:26:09:269"}
{"duration":18,"level":"info","message":"Graph: Create node (1 nodes) (18ms)","nodeCount":1,"operation":"Create node","relationshipCount":0,"timestamp":"2025-06-04 00:26:09:269"}
{"duration":50,"level":"info","message":"Graph: Create relationship (1 relationships) (50ms)","nodeCount":0,"operation":"Create relationship","relationshipCount":1,"timestamp":"2025-06-04 00:26:09:269"}
{"duration":17,"level":"info","message":"Graph: Create node (1 nodes) (17ms)","nodeCount":1,"operation":"Create node","relationshipCount":0,"timestamp":"2025-06-04 00:26:09:269"}
{"duration":43,"level":"info","message":"Graph: Create relationship (1 relationships) (43ms)","nodeCount":0,"operation":"Create relationship","relationshipCount":1,"timestamp":"2025-06-04 00:26:09:269"}
{"duration":8,"level":"info","message":"Graph: Create node (1 nodes) (8ms)","nodeCount":1,"operation":"Create node","relationshipCount":0,"timestamp":"2025-06-04 00:26:09:269"}
{"duration":7,"level":"info","message":"Graph: Create relationship (1 relationships) (7ms)","nodeCount":0,"operation":"Create relationship","relationshipCount":1,"timestamp":"2025-06-04 00:26:09:269"}
{"duration":7,"level":"info","message":"Graph: Create node (1 nodes) (7ms)","nodeCount":1,"operation":"Create node","relationshipCount":0,"timestamp":"2025-06-04 00:26:09:269"}
{"duration":11,"level":"info","message":"Graph: Create relationship (1 relationships) (11ms)","nodeCount":0,"operation":"Create relationship","relationshipCount":1,"timestamp":"2025-06-04 00:26:09:269"}
{"duration":8,"level":"info","message":"Graph: Create node (1 nodes) (8ms)","nodeCount":1,"operation":"Create node","relationshipCount":0,"timestamp":"2025-06-04 00:26:09:269"}
{"duration":8,"level":"info","message":"Graph: Create relationship (1 relationships) (8ms)","nodeCount":0,"operation":"Create relationship","relationshipCount":1,"timestamp":"2025-06-04 00:26:09:269"}
{"duration":9,"level":"info","message":"Graph: Create node (1 nodes) (9ms)","nodeCount":1,"operation":"Create node","relationshipCount":0,"timestamp":"2025-06-04 00:26:09:269"}
{"duration":9,"level":"info","message":"Graph: Create relationship (1 relationships) (9ms)","nodeCount":0,"operation":"Create relationship","relationshipCount":1,"timestamp":"2025-06-04 00:26:09:269"}
{"duration":292,"level":"info","message":"Graph: Update supplement knowledge (7 nodes) (7 relationships) (292ms)","nodeCount":7,"operation":"Update supplement knowledge","relationshipCount":7,"timestamp":"2025-06-04 00:26:09:269"}
{"level":"error","message":"Error parsing insight response: \"undefined\" is not valid JSON","stack":"SyntaxError: \"undefined\" is not valid JSON\n    at JSON.parse (<anonymous>)\n    at GemmaService.parseInsightResponse (/home/<USER>/Suplementor/backend/src/services/GemmaService.ts:294:27)\n    at GemmaService.generateInsights (/home/<USER>/Suplementor/backend/src/services/GemmaService.ts:105:19)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async SupplementResearchAgent.executeResearchPipeline (/home/<USER>/Suplementor/backend/src/agents/SupplementResearchAgent.ts:168:24)","timestamp":"2025-06-04 00:26:09:269"}
{"level":"info","message":"Agent completed for a7030ca1-6b15-4427-9330-ef7eb021c14e-74cc1731-cca0-4762-a499-b31ec414aa68","timestamp":"2025-06-04 00:26:09:269"}
